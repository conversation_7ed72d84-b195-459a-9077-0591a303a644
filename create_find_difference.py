from PIL import Image, ImageDraw, ImageFont
import os
from typing import List, Tuple
import requests
import json

class FindDifferenceGenerator:
    def __init__(self, 
                 main_char: str,
                 similar_chars: List[str],
                 background_color: Tuple[int, int, int],
                 text_color: Tuple[int, int, int] = (255, 255, 255),
                 width: int = 600,
                 height: int = 700):
        """
        初始化找不同生成器
        
        Args:
            main_char: 主体字符
            similar_chars: 相似字符列表（应该包含4个字符）
            background_color: 背景颜色RGB元组
            text_color: 文字颜色RGB元组，默认白色
            width: 图片宽度，默认600
            height: 图片高度，默认700
        """
        self.main_char = main_char
        if len(similar_chars) != 4:
            raise ValueError("similar_chars必须包含4个字符")
        self.similar_chars = similar_chars
        self.background_color = background_color
        self.text_color = text_color
        self.width = width
        self.height = height
        
    def _create_matrix(self) -> List[List[str]]:
        """创建8x8的文字矩阵，随机插入相似字符"""
        import random
        
        # 创建一个填充主体字符的8x8矩阵
        matrix = [[self.main_char for _ in range(8)] for _ in range(8)]
        
        # 随机选择4个不重复的位置插入相似字符
        positions = random.sample([(i, j) for i in range(8) for j in range(8)], 4)
        for (i, j), char in zip(positions, self.similar_chars):
            matrix[i][j] = char
            
        return matrix

    def generate(self, output_dir: str = "."):
        """
        生成找不同图片
        
        Args:
            output_dir: 输出目录，默认为当前目录
        """
        # 创建图片
        image = Image.new('RGB', (self.width, self.height), self.background_color)
        draw = ImageDraw.Draw(image)

        # 加载字体
        font_path = r"C:\Windows\Fonts\simhei.ttf"
        font = ImageFont.truetype(font_path, 60)

        # 计算单个字符的大小
        cell_width = int((self.width // 8) * 0.7)
        cell_height = int((self.height // 8) * 0.7)

        # 计算总的宽度和高度
        total_width = cell_width * 8
        total_height = cell_height * 8

        # 计算起始位置
        start_x = (self.width - total_width) // 2
        start_y = (self.height - total_height) // 2

        # 创建并获取文字矩阵
        text_matrix = self._create_matrix()

        # 绘制文字
        for i in range(8):
            for j in range(8):
                bbox = font.getbbox(text_matrix[i][j])
                text_width = bbox[2] - bbox[0]
                text_height = bbox[3] - bbox[1]
                
                x = start_x + j * cell_width + (cell_width - text_width) // 2
                y = start_y + i * cell_height + (cell_height - text_height) // 2
                
                draw.text((x, y), text_matrix[i][j], font=font, fill=self.text_color)

        # 保存图片
        filename = f"找不同-{self.main_char}-{''.join(self.similar_chars)}.png"
        output_path = os.path.join(output_dir, filename)
        image.save(output_path)
        print(f"图片已保存为 {output_path}")

def get_similar_chars(char: str) -> List[str]:
    """
    调用通义千问API获取相似字符
    
    Args:
        char: 主体字符
    Returns:
        List[str]: 4个相似字符的列表
    """
    url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    headers = {
        "Authorization": "Bearer " + "sk-4240848aa3314a2e8bef1c762bbfb305",  # 替换为你的API KEY
        "Content-Type": "application/json; charset=utf-8"
    }
    
#     prompt = f"""#任务：你是汉语言文学专家，请你按以下要求找出4个结构类似于"{char}"的汉字，用于制作找不到的游戏
    
# ##要求：
# 1. 结构类似于"{char}"字 
# 2. 不能是"{char}"字
# 3. 直接返回这4个字，不要其他解释，用空格分隔

# ##示例：
# 输入："卷"，返回"圈 券 巻 捲",
# 输入："爱"，返回"受 爰 夏 爽"
   
# ##再次重申：
# 直接返回这4个字，不要其他解释，用空格分隔

# 返回："""

    prompt = f"""#任务：你是汉语言文学专家，请在常用字中找出4个看着有点像"{char}"的汉字，又不能是"{char}"字，用于制作找不到的游戏。
#请直接输出这4个字！不要输出其他任何信息，用空格分隔："""

    request_body = {
        "model": "qwen-trubo",
        "messages": [
            {
                "role": "system",
                "content": "你是一个汉语言文学专家"
            },
            {
                "role": "user",
                "content": prompt
            }
        ]
    }

    try:
        response = requests.post(
            url,
            headers=headers,
            json=request_body,
            timeout=10
        )

        if response.status_code == 200:
            result = response.json()
            if result and result.get('choices') and result['choices'][0].get('message'):
                reply = result['choices'][0]['message']['content']
                print(f"API调用返回: {reply}")
                # 分割回复得到4个字符
                similar_chars = reply.strip().split()
                # 确保返回4个字符
                if len(similar_chars) == 4:
                    return similar_chars
                else:
                    # 如果返回的字符数量不对，使用备用数据
                    return _get_fallback_chars(char)
        
        print(f"API调用失败: {response.status_code} - {response.text}")
        return _get_fallback_chars(char)
    
    except Exception as e:
        print(f"API调用异常: {str(e)}")
        return _get_fallback_chars(char)

def _get_fallback_chars(char: str) -> List[str]:
    """
    当API调用失败时的备用相似字符
    """
    fallback_map = {
        "卷": ["圈", "券", "巻", "捲"],
        "思": ["恩", "息", "怒", "急"],
        "爱": ["受", "爰", "夏", "爽"],
        # 可以添加更多备用字符
    }
    return fallback_map.get(char, ["一", "二", "三", "四"])

def main():
    main_char = "爷"  # 主体字符
    # 备用：苟 日 觅 泪 
    background_color = (204, 0, 0) # 使用深红色
    # 红色背景 (255, 0, 0)  
    # 蓝色背景 (0, 0, 255)
    # 深红色 (139, 0, 0)
    
    # 获取相似字符
    # similar_chars = get_similar_chars(main_char)
    similar_chars = ["斧","父","妄","节"]
    
    # 创建生成器并生成图片
    generator = FindDifferenceGenerator(
        main_char=main_char,
        similar_chars=similar_chars,
        background_color=background_color
    )
    generator.generate(r"D:\work\ds\素材\pic")

if __name__ == "__main__":
    main()