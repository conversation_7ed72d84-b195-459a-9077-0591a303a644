// 基础设置
console.show();
toastLog("开始运行");

const { userName, productCode, promptRag, textInteract, voiceInteract, voiceSoundId, voiceInteractGap, popLink, popLinkGap, cdkey } = hamibot.env;

let TEXT_INTERACT_KEY = textInteract == 'y';
let VOICE_INTERACT_KEY = voiceInteract == 'y';
let VOICE_SOUND_ID = voiceSoundId;
let VOICE_INTERACT_GAP = voiceInteractGap;
let PROMPT_RAG = promptRag;
let USER_NAME = userName;
let CDKEY = cdkey;
let CDKEY_TYPE = "js";

// 定义文件夹路径
let BASE_FOLDER = "/storage/emulated/0/Download/douyinLive/resource/";
let PRODUCT_FOLDER = BASE_FOLDER + productCode;
let PRODUCT_INTRODUCE = PRODUCT_FOLDER + "/introduce.txt";
let PRODUCT_INTERACT = PRODUCT_FOLDER + "/interact.txt";
let INTERACT_FOLDER = files.cwd() + "/interactVoice/";
let INTRODUCE_FOLDER = PRODUCT_FOLDER + "/voice/";
let BACKGROUD_MUSIC_FOLDER = PRODUCT_FOLDER + "/music/";

// 全局变量存储prompt
let INTERACT_PROMPT = null;

// 记录上次语音回复的时间
let lastVoiceReplyTime = 0;

// 添加用户交互类型枚举
const INTERACTION_TYPE = {
    COMMENT: 'comment',
    ENTER: 'enter',
    LIKE: 'like',
    GIFT: 'gift'
};

// 添加全局背景音乐播放器
let backgroundPlayer = null;

// 添加全局变量记录上次点击的商品序号
let lastClickedIndex = 1;  // 默认为1
let isFirstClick = true;   // 添加首次点击标记
let lastProductLinkTime = 0;  // 添加上次商品链接时间记录

// 添加全局常量
const MAX_IDLE_TIME = 60000; // 最大空闲时间(毫秒)

// 添加全局变量来跟踪播放开始时间
global.playStartTime = 0;
const MAX_PLAY_DURATION = 180000; // 最大播放时间3分钟

// 添加全局变量存储API Keys和CDKey ID
let API_KEYS = null;
let CDKEY_ID = null;

// 添加验证和初始化API Keys的函数
async function validateAndInitAPIKeys() {
    try {
        console.log("开始验证");
        
        let url = `http://123.60.51.165:8000/api/validate_cdkey/js/${cdkey}`;
        
        let res = http.request(url, {
            method: "POST",
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({}),  // 添加空的请求体
            timeout: 10000  // 添加超时设置
        });

        // 检查响应对象是否存在
        if (!res) {
            console.error("API请求失败：未收到响应");
            toast("CDKey验证失败：网络请求异常");
            sleep(3000);
            exit();
        }

        // 检查响应体是否存在
        if (!res.body) {
            console.error("API响应异常：响应体为空");
            toast("CDKey验证失败：响应数据为空");
            sleep(3000);
            exit();
        }

        try {
            // 尝试解析响应数据
            let result = res.body.json();
            
            // 检查是否包含id字段
            if (result && result.id) {
                CDKEY_ID = result.id;
                console.log("CDKey验证通过");
            } else {
                console.error("API响应格式异常：" + JSON.stringify(result));
                toast("CDKey验证失败：响应格式异常");
                sleep(3000);
                exit();
            }
        } catch (parseError) {
            console.error("解析响应数据失败: " + parseError);
            console.error("原始响应数据: " + res.body.string());
            toast("CDKey验证失败：数据解析错误");
            sleep(3000);
            exit();
        }

        // 获取API Keys
        let apiKeysUrl = "http://123.60.51.165:8000/api/get_api_keys";
        let apiKeysRes = http.request(apiKeysUrl);

        if (apiKeysRes.statusCode !== 200) {
            console.error("获取API Keys失败");
            exit();
        }

        API_KEYS = apiKeysRes.body.json();
        console.log("API Keys初始化成功");
        
    } catch (e) {
        console.error("CDKey验证出错: " + e);
        console.error("错误堆栈: " + e.stack);
        
        if (e.toString().includes("ECONNREFUSED") || e.toString().includes("Failed to connect")) {
            toast("网络连接失败，请检查网络");
        } else if (e.toString().includes("timeout")) {
            toast("网络请求超时，请稍后重试");
        } else {
            toast("CDKey验证异常: " + e.message);
        }
        
        sleep(3000);
        exit();
    }
}

// 添加更新算力值的函数
async function updateTokens(tokens) {
    try {
        let updateUrl = `http://123.60.51.165:8000/api/update_tokens/${CDKEY_ID}/${tokens}`;
        let updateRes = http.request(updateUrl, {
            method: "PUT"
        });

        if (updateRes.statusCode !== 200) {
            let error = updateRes.body.json();
            console.error("更新算力值失败：" + error.detail);
            return false;
        }

        return true;
    } catch(e) {
        console.error("更新算力值出错：", e);
        return false;
    }
}

// 修改音频播放函数
function playAudio(path, callback) {
    let voicePlayer = new android.media.MediaPlayer();
    try {
        voicePlayer.setDataSource(path);
        voicePlayer.prepare();
        voicePlayer.setVolume(1.0, 1.0);
        voicePlayer.start();
        
        // 记录播放开始时间和状态
        global.playStartTime = Date.now();
        global.isCurrentlyPlaying = true;
        
        // 使用定时器检查播放状态
        let checkInterval = setInterval(function() {
            try {
                // 检查是否超时
                if (Date.now() - global.playStartTime > MAX_PLAY_DURATION) {
                    console.log("播放时间超过3分钟，强制结束播放");
                    clearInterval(checkInterval);
                    global.isCurrentlyPlaying = false;
                    try {
                        voicePlayer.reset();
                        voicePlayer.release();
                        if (callback) {
                            callback();
                        }
                    } catch(e) {
                        console.error("释放超时播放器出错：", e);
                    }
                    return;
                }

                if (!voicePlayer.isPlaying()) {
                    clearInterval(checkInterval);
                    global.isCurrentlyPlaying = false;
                    try {
                        voicePlayer.reset();
                        voicePlayer.release();
                        if (callback) {
                            callback();
                        }
                    } catch(e) {
                        console.error("释放播放器出错：", e);
                        if (callback) {
                            callback(e);
                        }
                    }
                }
            } catch(e) {
                clearInterval(checkInterval);
                global.isCurrentlyPlaying = false;
                console.error("播放状态检查出错：", e);
                try {
                    voicePlayer.reset();
                    voicePlayer.release();
                } catch(e2) {
                    console.error("释放播放器出错：", e2);
                }
                if (callback) {
                    callback(e);
                }
            }
        }, 100);

        // 添加播放完成监听器作为备用机制
        voicePlayer.setOnCompletionListener(new android.media.MediaPlayer.OnCompletionListener({
            onCompletion: function(mp) {
                clearInterval(checkInterval);
                global.isCurrentlyPlaying = false;
                try {
                    voicePlayer.reset();
                    voicePlayer.release();
                    if (callback) {
                        callback();
                    }
                } catch(e) {
                    console.error("播放完成处理出错：", e);
                    if (callback) {
                        callback(e);
                    }
                }
            }
        }));
        
    } catch(e) {
        global.isCurrentlyPlaying = false;
        console.error("播放初始化出错：", e);
        try {
            voicePlayer.reset();
            voicePlayer.release();
        } catch(e2) {
            console.error("释放播放器出错：", e2);
        }
        if (callback) {
            callback(e);
        }
    }
}

// 评论互动处理
function handleComments() {
    console.log("开始处理评论");
    let history = [];
    const MAX_HISTORY_SIZE = 50;
    
    while(true) {
        try {            
            // 查找评论列表
            let commentList = className("androidx.recyclerview.widget.RecyclerView").findOne(2000);
            if (!commentList) {
                sleep(1000);
                continue;
            }

            // 获取所有评论项
            let comments = commentList.children();
            if (comments.length === 0) {
                console.log("暂无评论");
                sleep(1000);
                continue;
            }

            // 遍历评论
            for (let i = 0; i < comments.length; i++) {
                let commentItem = comments[i];
                if (!commentItem) continue;

                let textView = commentItem.findOne(className("android.widget.TextView"));
                if (!textView) continue;

                let content = textView.text();
                if (!content) {
                    content = commentItem.desc();
                }
                if (!content) continue;

                // 处理评论内容
                content = content.replace(/\u200E/g, "").trim();

                // 跳过自己发送的消息
                if (content.includes(USER_NAME)) {
                    continue;
                }
                
                // 检查是否是历史评论
                if (history.includes(content)) {
                    continue;
                }
                
                // 判断交互类型
                let interactionType = getInteractionType(content);
                let username = extractUsername(content);

                // 获取评论时间
                let timeView = commentItem.findOne(className("android.widget.TextView").textMatches(/.*(分钟前|刚刚).*/));
                if (timeView) {
                    let timeText = timeView.text();
                    // 只处理"刚刚"或"1分钟前"的评论
                    if (!timeText.includes("刚刚") && !timeText.includes("1分钟前")) {
                        history.push(content);
                        continue;
                    }
                }

                if (!username) continue;
                
                console.log(`发现新交互：${interactionType} - ${content}`);

                // 根据不同类型处理交互
                switch (interactionType) {
                    case INTERACTION_TYPE.COMMENT:
                        let reply = null;
                        if (TEXT_INTERACT_KEY || VOICE_INTERACT_KEY) {
                            reply = generateReply(content);
                        }
                        
                        if (reply && !reply.trim().includes("请稍等")) {
                            if (TEXT_INTERACT_KEY) {
                                sendReply(commentItem, reply);
                            }
                            if (VOICE_INTERACT_KEY) {
                                let ttsText = username + "宝子，" + reply;
                                sendVoiceReply(ttsText);
                            }
                        }
                        break;
                        
                    case INTERACTION_TYPE.ENTER:
                        if (VOICE_INTERACT_KEY) {
                            let ttsText = `欢迎${username}进入我的直播间`;
                            sendVoiceReply(ttsText);
                        }
                        break;
                    case INTERACTION_TYPE.GIFT:
                        if (VOICE_INTERACT_KEY) {
                            let ttsText = `感谢${username}送的礼物`;
                            lastVoiceReplyTime = 0;
                            sendVoiceReply(ttsText);
                        }
                        break;
                }
                
                // 记录已处理的交互
                history.push(content);
                if (history.length > MAX_HISTORY_SIZE) {
                    history.shift();  // 删除数组第一个元素
                }
            }

            sleep(5000);

        } catch(e) {
            console.error("评论处理出错：", e);
            sleep(5000);
        }
    }
}
// 生成回复内容
function generateReply(content) {
    try {
        if (!INTERACT_PROMPT) {
            console.error("prompt未初始化");
            return null;
        }

        // 计算并更新算力值（固定1算力）
        if (!updateTokens(1)) {
            console.log("算力不足，跳过回复生成");
            return null;
        }
        
        let prompt = INTERACT_PROMPT.replace(/{{content}}/g, content);
        let url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
        
        // 获取openai服务的API Key
        let openaiKey = API_KEYS.find(key => key.service === 'openai');
        if (!openaiKey) {
            console.error("未找到openai服务的API Key");
            return null;
        }
        
        let headers = {
            "Authorization": `Bearer ${openaiKey.key}`,
            "Content-Type": "application/json; charset=utf-8"
        };
        let requestBody = {
            "model": "qwen-turbo",
            "messages": [
                {
                    "role": "system",
                    "content": "你是基于大语言模型的AI智能助手，旨在回答并解决人们的任何问题"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        };

        // 发送请求
        let res = http.request(url, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(requestBody),
            timeout: 10000
        });

        if (res.statusCode === 200) {
            let result = res.body.json();
            if (result && result.choices && result.choices[0] && result.choices[0].message) {
                let reply = result.choices[0].message.content;
                return reply;
            } else {
                console.error("API响应格式错误：" + JSON.stringify(result));
                return null;
            }
        } else {
            console.error("API请求失败：" + res.statusCode + " - " + res.body.string());
            return null;
        }

    } catch (e) {
        console.error("生成回复出错：", e);
        return null;
    }
}
// 在发送回复前分析结构
function sendReply(commentItem, content) {
    try {
        // 查找评论文本元素
        let textView = commentItem.findOne(className("android.widget.TextView"));
        if (!textView) {
            console.log("未找到评论文本元素");
            // 点击退出
            click(900, 600);
            return;
        }
        
        // 直接点击文本元素
        textView.click();
        sleep(1000);

        // 查找可点击的"回复TA"按钮
        let menuItems = className("android.widget.LinearLayout").find();
        let found = false;
        for (let item of menuItems) {
            if (item.clickable() && item.findOne(text("回复TA"))) {
                item.click();
                found = true;
                sleep(1000);
                break;
            }
        }
        
        if (!found) {
            console.log("未找到回复TA按钮");
            // 点击退出
            click(900, 600);
            return;
        }

        // 查找输入框
        let inputBox = className("android.widget.EditText").findOne(2000);
        if (!inputBox) {
            console.log("未找到输入框");
            // 点击退出
            click(900, 600);
            return;
        }

        // 在原有内容后追加回复内容
        inputBox.click();  // 先点击输入框
        sleep(500);
        setClip(content);  // 置剪贴板内容
        sleep(500);
        paste();  // 粘贴内容
        sleep(1000);

        // 点击发送
        let sendBtn = text("发送").findOne(2000);
        if (!sendBtn) {
            console.log("未找到发送按钮");
            // 点击退出
            click(900, 600);
            return;
        }
        sendBtn.click();
        sleep(1000);

    } catch(e) {
        console.error("发送回复失败：", e);
        // 发生任何错误时也点击退出
        sleep(1000);
        click(900, 600);
    }
}

function sendVoiceReply(text) {
    try {
        // 检查是否在冷却时间内
        let now = Date.now();
        if (now - lastVoiceReplyTime < VOICE_INTERACT_GAP * 1000) {
            console.log("语音回复冷却中，跳过回复");
            return;
        }

        // 计算算力值（每个字符0.1算力）
        let tokens = Math.ceil(text.length * 0.1);
        if (!updateTokens(tokens)) {
            console.log("算力不足，跳过语音回复");
            return;
        }
        
        // 获取tts-max服务的API Key
        let ttsKey = API_KEYS.find(key => key.service === 'tts-max');
        if (!ttsKey) {
            console.error("未找到tts-max服务的API Key");
            return;
        }
        
        let url = "https://api.minimax.chat/v1/t2a_v2?GroupId=1836702808724738844";
        let headers = {
            "Authorization": `Bearer ${ttsKey.key}`,
            "Content-Type": "application/json"
        };
        let requestBody = {
            "model": "speech-01-turbo",
            "text": text,
            "stream": false,
            "voice_setting": {
                "voice_id": VOICE_SOUND_ID || "ttv-voice-2024112722551824-IG1c5u0Q",
                "speed": 1.1,
                "vol": 1,
                "pitch": 0
            }
        };

        // 发送请求
        let res = http.request(url, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(requestBody)
        });

        if (res.statusCode === 200) {
            let result = res.body.json();
            if (result.base_resp.status_code === 0) {
                let audioData = result.data.audio;
                let fileName = "voice_" + Date.now() + ".mp3";
                let filePath = INTERACT_FOLDER + fileName;
                
                // 修改解码方式，确保byte值在正确范围内
                let bytes = [];
                for (let i = 0; i < audioData.length; i += 2) {
                    let value = parseInt(audioData.substr(i, 2), 16);
                    // 将无符号字节(0-255)转换为有符号字节(-128到127)
                    bytes.push(value > 127 ? value - 256 : value);
                }
                
                let byteArray = java.lang.reflect.Array.newInstance(java.lang.Byte.TYPE, bytes.length);
                for (let i = 0; i < bytes.length; i++) {
                    byteArray[i] = bytes[i];
                }
                
                files.writeBytes(filePath, byteArray);
                lastVoiceReplyTime = Date.now();
            } else {
                console.error("TTS API响应错误：" + JSON.stringify(result.base_resp));
            }
        } else {
            console.error("TTS API请求失败：" + res.statusCode + " - " + res.body.string());
        }

    } catch (e) {
        console.error("生成语音回复出错：", e);
    }
}

// 语音播放处理
function handleVoice() {
    let isRunning = true;
    let lastPlayTime = Date.now();
    let currentPlayer = null; // 添加当前播放器的引用

    function playNext() {
        if (!isRunning) return;
        
        try {
            // 重置错误计数器
            if (!global.errorCount) {
                global.errorCount = 0;
            }

            // 检查并清理当前播放器
            if (currentPlayer) {
                try {
                    currentPlayer.reset();
                    currentPlayer.release();
                } catch(e) {
                    console.error("清理播放器失败:", e);
                }
                currentPlayer = null;
            }

            let interactFiles = java.io.File(INTERACT_FOLDER).list();
            if (!interactFiles) {
                interactFiles = [];
            }
            interactFiles = interactFiles.filter(function(name) {
                return name.endsWith('.mp3') || name.endsWith('.aac');
            });
            
            if(interactFiles.length > 0) {
                let path = INTERACT_FOLDER + interactFiles[0];
                playAudio(path, function(error) {
                    if (error) {
                        console.error("播放interact语音出错", error);
                        global.errorCount++;
                        // 如果连续错误超过3次，重新初始化播放循环
                        if (global.errorCount > 3) {
                            global.errorCount = 0;
                            console.log("检测到连续播放错误，重新初始化播放循环");
                            sleep(3000);
                            handleVoice();
                            return;
                        }
                    } else {
                        global.errorCount = 0;
                    }
                    java.io.File(path).delete();
                    sleep(1000);
                    playNext();
                });
                return;
            }

            let introduceFiles = java.io.File(INTRODUCE_FOLDER).list();
            if (!introduceFiles) {
                introduceFiles = [];
            }
            introduceFiles = introduceFiles.filter(function(name) {
                return name.endsWith('.mp3') || name.endsWith('.aac');
            });
            
            if(introduceFiles.length > 0) {
                let randomIndex = Math.floor(Math.random() * introduceFiles.length);
                let fileName = introduceFiles[randomIndex];
                let path = INTRODUCE_FOLDER + fileName;
                
                // 检查文件名是否包含_link
                let productKey = null;
                let linkMatch = fileName.replace('.mp3', '').match(/_link(\d+)/);
                if (linkMatch) {
                    productKey = linkMatch[1];
                }

                playAudio(path, function(error) {
                    if (error) {
                        console.error("播放介绍语音出错：", error);
                        global.errorCount++;
                        if (global.errorCount > 3) {
                            global.errorCount = 0;
                            console.log("检测到连续播放错误，重新初始化播放循环");
                            sleep(3000);
                            handleVoice();
                            return;
                        }
                    } else {
                        global.errorCount = 0;
                        console.log("播放介绍语音：" + fileName);
                        
                        // 如果有商品关键字且popLink不为'n',处理商品链接
                        if (productKey && popLink !== 'n') {
                            threads.start(function() {
                                try {
                                    // 检查冷却时间
                                    let now = Date.now();
                                    if (now - lastProductLinkTime < (popLinkGap * 1000)) { // 使用配置的间隔时间
                                        return;
                                    }
                                    
                                    console.log("开始处理商品链接，序号：" + productKey);
                                    
                                    // 根据popLink类型查找不同的按钮
                                    let targetText = popLink === 'group' ? "团购" : "商品";
                                    let viewGroups = className("android.view.ViewGroup").find();
                                    
                                    let targetViewGroup = null;
                                    for (let group of viewGroups) {
                                        let textView = group.findOne(className("android.widget.TextView").text(targetText));
                                        if (textView) {
                                            
                                            // 从团购文本开始向上查找合适的可点击容器
                                            let currentParent = textView.parent();
                                            let maxTries = 3;
                                            let tries = 0;
                                            
                                            while (currentParent && tries < maxTries) {
                                                if (currentParent.clickable() && currentParent.bounds().height() < 300) {
                                                    targetViewGroup = currentParent;
                                                    break;
                                                }
                                                currentParent = currentParent.parent();
                                                tries++;
                                            }
                                            
                                            if (targetViewGroup) {
                                                // 点击打开弹窗
                                                let bounds = targetViewGroup.bounds();
                                                click(bounds.centerX(), bounds.centerY());
                                                
                                                // 等待弹窗加载
                                                sleep(3000);
                                                
                                                // 如果是第一次点击，先重置位置
                                                if (isFirstClick) {
                                                    console.log("首次点击，重置商品列表位置");
                                                    // 快速向下滑动5次，确保回到顶部
                                                    for (let i = 0; i < 5; i++) {
                                                        swipe(540, 1000, 540, 1500, 300);
                                                        sleep(500);
                                                    }
                                                    isFirstClick = false;  // 更新标记
                                                    lastClickedIndex = 1;  // 重置位置后，当前位置为1
                                                }
                                                
                                                // 获取当前要点击的商品序号
                                                let currentIndex = parseInt(productKey);
                                                
                                                // 计算需要滑动的次数和方向
                                                let scrollDistance = currentIndex - lastClickedIndex;
                                                
                                                if (scrollDistance !== 0) {
                                                    // 根据距离决定滑动方向和次数
                                                    let scrollTimes = Math.abs(scrollDistance);
                                                    let isScrollUp = scrollDistance > 0;
                                                    
                                                    console.log(`需要${isScrollUp ? '上' : '下'}划${scrollTimes}次`);
                                                    
                                                    for (let i = 0; i < scrollTimes; i++) {
                                                        if (isScrollUp) {
                                                            // 上划
                                                            swipe(540, 1500, 540, 1000, 500);
                                                        } else {
                                                            // 下划
                                                            swipe(540, 1000, 540, 1500, 500);
                                                        }
                                                        sleep(1000);
                                                    }
                                                }
                                                
                                                // 点击讲解按钮
                                                click(950, 1510);
                                                
                                                // 更新上次点击的序号和时间
                                                lastClickedIndex = currentIndex;
                                                lastProductLinkTime = Date.now();  // 记录本次操作时间
                                                
                                                // 等待点击效果
                                                sleep(1500);
                                                
                                                // 点击退出弹窗
                                                click(900, 600);
                                                
                                                break;
                                            }
                                        }
                                    }
                                    
                                    if (!targetViewGroup) {
                                        console.log("未找到正确的团购按钮组件");
                                    }
                                    
                                } catch(e) {
                                    console.error("处理商品链接失败：", e);
                                    sleep(1000);
                                    console.log("错误后点击退出弹窗");
                                    click(900, 600);
                                }
                            });
                        }
                    }
                    sleep(1000);
                    playNext();
                });
            } else {
                sleep(5000);
                playNext();
            }

            // 更新最后播放时间
            lastPlayTime = Date.now();

        } catch(e) {
            console.error("播放循环出错：", e);
            global.errorCount++;
            if (global.errorCount > 3) {
                global.errorCount = 0;
                console.log("检测到连续错误，强制重启播放循环");
                isRunning = false;
                sleep(1000);
                isRunning = true;
                playNext();
                return;
            }
            sleep(3000);
            playNext();
        }
    }

    // 改进重启方法
    function forceRestart() {
        console.log("执行强制重启...");
        isRunning = false;
        
        // 清理当前播放器
        if (currentPlayer) {
            try {
                currentPlayer.reset();
                currentPlayer.release();
            } catch(e) {
                console.error("清理播放器失败:", e);
            }
            currentPlayer = null;
        }
        
        // 重置所有状态
        global.errorCount = 0;
        lastPlayTime = Date.now();
        
        // 延迟后重新启动
        sleep(2000);
        isRunning = true;
        
        // 在新线程中启动播放
        threads.start(function() {
            playNext();
        });
        
        console.log("重启完成");
    }

    // 启动播放循环
    threads.start(function() {
        playNext();
    });

    // 返回改进的控制接口
    return {
        stop: function() {
            isRunning = false;
        },
        restart: function() {
            forceRestart();
        },
        getLastPlayTime: function() {
            return lastPlayTime;
        },
        isRunning: function() {
            return isRunning;
        }
    };
}

// 创建必要的文件夹
function initFolders() {
    try {
        // 确保私有目录存在
        let interactDir = new java.io.File(INTERACT_FOLDER);
        if (!interactDir.exists()) {
            interactDir.mkdirs();
            console.log("创建互动语音临时文件夹：" + INTERACT_FOLDER);
        }
        
        // 外部存储的目录
        let introduceDir = new java.io.File(INTRODUCE_FOLDER);
        if (!introduceDir.exists()) {
            introduceDir.mkdirs();
            console.log("创建商品话术文件夹：" + INTRODUCE_FOLDER);
        }
    } catch(e) {
        console.error("创建文件夹失败：", e);
    }
}

// 提取用户名函数
function extractUsername(content) {
    try {
        let username = "";
        // 处理不同格式的评论
        if (content.includes("：")) {
            username = content.split("：")[0];
        } else if (content.includes(":")) {  // 处理英文冒号
            username = content.split(":")[0];
        } else if (content.includes("为主播点赞了")) {
            username = content.split("为主播点赞了")[0];
        } else {
            // 使用正则匹配"通过xxx来了"的格式
            let match = content.match(/(.*?)通过.*?来了/);
            if (match) {
                username = match[1];
            } else if (content.includes("来了")) {
                username = content.split("来了")[0];
            }
        }
        
        // 清理用户名
        return username.trim();
    } catch(e) {
        console.error("提取用户名出错：", e);
        return "";
    }
}

// 初始化函数中添加prompt加载
function initSystem() {
    console.log("初始化系统...");
    
    // 初始化文件夹
    initFolders();
    
    // 加载prompt
    try {
        // 优先检查PRODUCT_INTERACT文件
        if(files.exists(PRODUCT_INTERACT)) {
            INTERACT_PROMPT = files.read(PRODUCT_INTERACT);
            console.log("使用互动话术文件作为prompt");
        } else {
            let promptText = `###任务：
你是抖音直播间的控场助理，请根据现在正在直播的播报内容，回复用户的评论，做到简洁明了，不要错误回答，回了不了的问题回复 请稍等。

###播报内容：
{{introduce}}

###用户评论：{{content}}

###注意：
1. 回复内容一定要简洁，亲切，要能够吸引用户购买，不要超过30个字。
2. 输出内容为直接回复客户的内容，不能重复用户问题，不需要包含自己的角色。

直接输出：`;
            let introduceText = files.read(PRODUCT_INTRODUCE);
            if (!introduceText) {
                console.error("未找到商品直播稿文件：" + PRODUCT_INTRODUCE);
                exit();
            }
            if (PROMPT_RAG) {
                introduceText = introduceText + "补充知识：\n" + PROMPT_RAG;
            }
            INTERACT_PROMPT = promptText.replace(/{{introduce}}/g, introduceText);
        }
    } catch(e) {
        console.error("加载商品介绍失败：", e);
        exit();
    }
}

// 添加判断交互类型的函数
function getInteractionType(content) {
    if (content.includes("送出")) {
        return INTERACTION_TYPE.GIFT;
    } else if (content.includes("：") || content.includes(":")) {
        return INTERACTION_TYPE.COMMENT;
    } else if (content.includes("为你点赞了")) {
        return INTERACTION_TYPE.LIKE;
    } else if (content.match(/(.*?)通过.*?来了/) || content.includes("来了")) {
        return INTERACTION_TYPE.ENTER;
    }
    return null;
}

// 添加背景音乐处理函数
function handleBackgroundMusic() {
    let isPlaying = true; // 添加播放状态标志

    function playBackgroundMusic() {
        while (isPlaying) {
            try {
                console.log("开始检查背景音乐文件夹...");
                let musicFiles = java.io.File(BACKGROUD_MUSIC_FOLDER).list();
                if (!musicFiles || musicFiles.length === 0) {
                    console.log("背景音乐文件夹为空");
                    sleep(5000);
                    continue;
                }

                musicFiles = musicFiles.filter(function(name) {
                    return name.endsWith('.mp3');
                });

                if (musicFiles.length === 0) {
                    console.log("没有找到mp3文件");
                    sleep(5000);
                    continue;
                }

                // 随机选择一首音乐播放
                let randomIndex = Math.floor(Math.random() * musicFiles.length);
                let musicPath = BACKGROUD_MUSIC_FOLDER + musicFiles[randomIndex];
                console.log("播放背景音乐：" + musicFiles[randomIndex]);

                // 创建新的播放器实例
                let player = new android.media.MediaPlayer();
                
                try {
                    player.setDataSource(musicPath);
                    player.prepare();
                    player.setVolume(0.05, 0.05);
                    player.start();

                    // 等待播放完成
                    while (player.isPlaying()) {
                        sleep(1000);
                    }
                } catch(e) {
                    console.error("音乐播放出错：", e);
                } finally {
                    try {
                        player.reset();
                        player.release();
                    } catch(e) {
                        console.error("释放播放器出错：", e);
                    }
                }

                sleep(1000); // 播放间隔
                
            } catch(e) {
                console.error("背景音乐循环出错：", e);
                sleep(3000);
            }
        }
    }

    // 启动背景音乐循环
    threads.start(function() {
        playBackgroundMusic();
    });

    // 添加停止播放的方法（如果需要）
    return {
        stop: function() {
            isPlaying = false;
        }
    };
}

// 主函数
async function main() {
    try {
        console.log("开始初始化...");
        
        // 添加CDKey验证和API Keys初始化
        await validateAndInitAPIKeys();
        
        initSystem();
        
        if (TEXT_INTERACT_KEY || VOICE_INTERACT_KEY) {
            threads.start(function() {  
                handleComments();
            });
        }
        
        let voiceController = handleVoice();
        let backgroundMusicController = handleBackgroundMusic();
        
        // 添加主线程存活检查
        setInterval(() => {
            console.log("主线程正在运行...");
        }, 30000);
        
        // 改进主循环中的监控逻辑
        threads.start(function() {
            while(true) {
                sleep(30000); // 每30秒检查一次
                try {
                    let now = Date.now();
                    let lastPlay = voiceController.getLastPlayTime();
                    
                    // 检查是否播放卡住
                    if (global.isCurrentlyPlaying && now - global.playStartTime > MAX_PLAY_DURATION) {
                        console.log("检测到播放可能卡住，强制重置播放状态");
                        global.isCurrentlyPlaying = false;
                        global.playStartTime = 0;
                    }
                    
                    // 检查空闲状态
                    if (!global.isCurrentlyPlaying && 
                        now - lastPlay > MAX_IDLE_TIME && 
                        voiceController.isRunning()) {
                        console.log("检测到语音系统异常（超过1分钟无播放活动），执行强制重启");
                        voiceController.restart();
                        sleep(3000);
                        if (Date.now() - voiceController.getLastPlayTime() > MAX_IDLE_TIME) {
                            console.log("首次重启可能未成功，尝试二次重启");
                            voiceController.restart();
                        }
                    }
                } catch(e) {
                    console.error("监控检查出错：", e);
                }
            }
        });
    } catch (e) {
        console.error("主函数执行出错：", e);
        // 出错后等待一段时间再退出
        sleep(5000);
        exit();
    }
}

// 修改脚本结尾部分
// 移除原有的while循环，改用setInterval保持脚本运行
main().catch(e => {
    console.error("脚本执行出错：", e);
    sleep(5000);
});

// 使用events模块保持脚本运行
events.observeKey();

events.on("key", function(keyCode, event) {
    console.log("按键事件：" + keyCode);
});

events.on("exit", function() {
    console.log("脚本退出");
});

// 添加心跳检测
setInterval(() => {
    console.log("脚本保持运行中...");
}, 10000);
