// 基础设置
console.show();
toastLog("开始运行");

// 定义文件夹路径
var INTERACT_FOLDER = "/storage/emulated/0/Download/douyinLive/resource/productTest/voice/interact/";
var INTRODUCE_FOLDER = "/storage/emulated/0/Download/douyinLive/resource/productTest/voice/introduce/";

// 音频播放函数
function playAudio(path, callback) {
    var player = new android.media.MediaPlayer();
    try {
        console.log("准备播放音频：" + path);
        player.setDataSource(path);
        player.prepare();
        player.start();
        
        // 获取音频时长
        var duration = player.getDuration();
        console.log("音频时长：" + duration + "ms");
        
        // 使用定时器检查播放状态
        var checkInterval = setInterval(function() {
            try {
                if (!player.isPlaying()) {
                    clearInterval(checkInterval);
                    console.log("检测到音频播放完成：" + path);
                    try {
                        player.reset();
                        player.release();
                        console.log("播放器已释放");
                        if (callback) {
                            callback();
                        }
                    } catch(e) {
                        console.error("释放播放器出错：", e);
                        if (callback) {
                            callback(e);
                        }
                    }
                }
            } catch(e) {
                clearInterval(checkInterval);
                console.error("播放状态检查出错：", e);
                try {
                    player.reset();
                    player.release();
                } catch(e2) {
                    console.error("释放播放器出错：", e2);
                }
                if (callback) {
                    callback(e);
                }
            }
        }, 100);  // 每100ms检查一次
        
        // 添加错误监听器
        player.setOnErrorListener(new android.media.MediaPlayer.OnErrorListener({
            onError: function(mp, what, extra) {
                clearInterval(checkInterval);
                console.error("播放器错误 - what: " + what + ", extra: " + extra);
                try {
                    player.reset();
                    player.release();
                } catch(e) {
                    console.error("释放播放器出错：", e);
                }
                if (callback) {
                    callback(new Error("播放器错误：" + what));
                }
                return true;
            }
        }));
        
    } catch(e) {
        console.error("播放初始化出错：", e);
        try {
            player.reset();
            player.release();
        } catch(e2) {
            console.error("释放播放器出错：", e2);
        }
        if (callback) {
            callback(e);
        }
    }
}

// 评论互动处理
function handleComments() {
    var history = {};  // 用于记录已处理的评论
    var lastProcessedTime = Date.now();  // 记录开始运行的时间
    
    while(true) {
        try {
            // 查找评论列表
            let commentList = className("androidx.recyclerview.widget.RecyclerView").findOne(2000);
            if (!commentList) {
                console.log("未找到评论列表，继续等待...");
                sleep(1000);
                continue;
            }

            // 获取所有评论项
            let comments = commentList.children();
            if (comments.length === 0) {
                console.log("暂无评论");
                sleep(1000);
                continue;
            }

            // 遍历评论
            for (let i = 0; i < comments.length; i++) {
                let commentItem = comments[i];
                if (!commentItem) continue;

                // 获取评论文本
                let textView = commentItem.findOne(className("android.widget.TextView"));
                if (!textView) continue;

                let content = textView.text();
                if (!content) {
                    content = commentItem.desc();
                }
                if (!content) continue;

                // 处理评论内容
                content = content.replace(/\u200E/g, "").trim();
                
                // 检查是否是历史评论
                if (history[content]) {
                    // console.log("跳过已处理的评论：" + content);  // 可以注释掉以减少日志输出
                    continue;
                }

                // 获取评论时间（如果UI中有时间信息的话）
                let timeView = commentItem.findOne(className("android.widget.TextView").textMatches(/.*(分钟前|刚刚).*/));
                let isNewComment = true;
                if (timeView) {
                    let timeText = timeView.text();
                    // 只处理"刚刚"或"1分钟前"的评论
                    isNewComment = timeText.includes("刚刚") || timeText.includes("1分钟前");
                }

                // 只处理新评论
                if (!isNewComment) {
                    history[content] = true;  // 记录旧评论，避免重复处理
                    continue;
                }

                console.log("发现新评论：" + content);

                // 提取用户名
                let username = extractUsername(content);
                if (username) {
                    console.log("评论用户：" + username);
                    
                    // 生成回复内容
                    let reply = generateReply(content);
                    if (reply) {
                        // 发送回复
                        //let fullReply = "@" + username + " " + reply;
                        sendReply(reply);
                        
                        // 记录已处理的评论
                        history[content] = true;
                        
                        // 限制历史记录大小，保留最近1000条
                        let historyKeys = Object.keys(history);
                        if (historyKeys.length > 1000) {
                            // 删除最早的500条记录
                            historyKeys.slice(0, 500).forEach(key => delete history[key]);
                        }
                    }
                }
            }

            // 避免过于频繁的检查
            sleep(1000);

        } catch(e) {
            console.error("评论处理出错：", e);
            sleep(3000);
        }
    }
}

// 生成回复内容
function generateReply(content) {
    // 这里可以根据评论内容生成相应的回复
    // 示例：简单的关键词匹配
    if (content.includes("价格")) {
        return "亲，详情请看直播间介绍哦~";
    } else if (content.includes("发货")) {
        return "当天下单当天发货，全国包邮哦~";
    } else if (content.includes("质量")) {
        return "我们的产品都是正品保证，请放心购买~";
    } else if (content.includes("优惠") || content.includes("便宜")) {
        return "现在正在做活动，下单立减，先到先得哦~";
    } else {
        return "感谢您的支持，欢迎关注直播间~";
    }
    
    // 如果没有匹配到关键词，返回null表示不需要回复
    return null;
}

// 发送回复的具体实现
function sendReply(content) {
    try {
        console.log("准备发送回复：" + content);
        
        // 点击评论按钮
        let commentBtn = text("评论").findOne(2000);
        if (!commentBtn) {
            console.log("未找到评论按钮");
            return;
        }
        commentBtn.click();
        sleep(1000);

        // 输入回复内容
        let inputBox = className("android.widget.EditText").findOne(2000);
        if (!inputBox) {
            console.log("未找到输入框");
            return;
        }
        inputBox.setText(content);
        sleep(1000);

        // 点击发送
        let sendBtn = text("发送").findOne(2000);
        if (!sendBtn) {
            console.log("未找到发送按钮");
            return;
        }
        sendBtn.click();
        console.log("回复发送成功：" + content);
        sleep(1000);

    } catch(e) {
        console.error("发送回复失败：", e);
    }
}

// 语音播放处理
function handleVoice() {
    function playNext() {
        try {
            console.log("开始检查互动话术文件...");
            var interactFiles = java.io.File(INTERACT_FOLDER).list();
            if (!interactFiles) {
                interactFiles = [];
            }
            interactFiles = interactFiles.filter(function(name) {
                return name.endsWith('.mp3') || name.endsWith('.aac');
            });
            
            if(interactFiles.length > 0) {
                var path = INTERACT_FOLDER + interactFiles[0];
                console.log("找到互动话术：" + path);
                playAudio(path, function(err) {
                    if (err) {
                        console.error("播放出错：", err);
                    } else {
                        console.log("准备删除文件：" + path);
                        java.io.File(path).delete();
                    }
                    console.log("等待1秒后播放下一个...");
                    sleep(1000);
                    console.log("开始播放下一个");
                    playNext();
                });
                return;
            }

            console.log("开始检查商品话术文件...");
            var introduceFiles = java.io.File(INTRODUCE_FOLDER).list();
            if (!introduceFiles) {
                introduceFiles = [];
            }
            introduceFiles = introduceFiles.filter(function(name) {
                return name.endsWith('.mp3') || name.endsWith('.aac');
            });
            
            if(introduceFiles.length > 0) {
                var randomIndex = Math.floor(Math.random() * introduceFiles.length);
                var path = INTRODUCE_FOLDER + introduceFiles[randomIndex];
                console.log("找到商品话术：" + path);
                playAudio(path, function(err) {
                    if (err) {
                        console.error("播放出错：", err);
                    }
                    console.log("等待1秒后播放下一个...");
                    sleep(1000);
                    console.log("开始播放下一个");
                    playNext();
                });
            } else {
                console.log("没有可用的语音文件，等待5秒...");
                sleep(5000);
                console.log("重新检查文件...");
                playNext();
            }
        } catch(e) {
            console.error("播放循环出错：", e);
            sleep(3000);
            console.log("尝试恢复播放...");
            playNext();
        }
    }

    // 启动播放循环
    console.log("启动语音播放线程...");
    threads.start(function() {
        playNext();
    });
}

// 创建必要的文件夹
function initFolders() {
    try {
        var interactDir = new java.io.File(INTERACT_FOLDER);
        var introduceDir = new java.io.File(INTRODUCE_FOLDER);
        
        if (!interactDir.exists()) {
            interactDir.mkdirs();
            console.log("创建互动话术文件夹：" + INTERACT_FOLDER);
        }
        if (!introduceDir.exists()) {
            introduceDir.mkdirs();
            console.log("创建商品话术文件夹：" + INTRODUCE_FOLDER);
        }
    } catch(e) {
        console.error("创建文件夹失败：", e);
    }
}

// 提取用户名函数
function extractUsername(content) {
    try {
        let username = "";
        // 处理不同格式的评论
        if (content.includes("：")) {
            username = content.split("：")[0];
        } else if (content.includes("通过流量扶持来了")) {
            username = content.split("通过流量扶持来了")[0];
        } else if (content.includes("来了")) {
            username = content.split("来了")[0];
        } else if (content.includes(":")) {  // 处理英文冒号
            username = content.split(":")[0];
        }
        
        // 清理用户名
        username = username.trim();
        console.log("提取到用户名：" + username);
        return username;
    } catch(e) {
        console.error("提取用户名出错：", e);
        return "";
    }
}

// 主函数
function main() {
    console.log("初始化...");
    initFolders();
    
    threads.start(function() {
        handleComments();
    });
    
    handleVoice();  // 直接调用，不再开新线程
}

// 启动主程序
main();

// 使用events模块保持脚本运行
events.observeKey();

events.on("key", function(keyCode, event) {
    console.log("按键事件：" + keyCode);
});

events.on("exit", function() {
    console.log("脚本退出");
});

// 保持脚本运行
while(true) {
    sleep(1000);
}