// 基础设置
console.show();
toastLog("开始运行");

const { userName, productCode, promptRag, textInteract, voiceInteract, voiceSoundId, voiceInteractGap, popLink, popLinkGap, cdkey, allVoiceInteractType, thanksType } = hamibot.env;

// 添加环境变量检查
if (!cdkey) {
    console.error("错误：未设置CDKey");
    toast("请在配置中设置CDKey");
    sleep(3000);
    exit();
}

// 其他变量设置
let TEXT_INTERACT_KEY = textInteract == 'y';
let VOICE_INTERACT_KEY = voiceInteract == 'y';
let VOICE_SOUND_ID = voiceSoundId.includes('meteorfly') ? 'meteorflymale026' : voiceSoundId;
let VOICE_INTERACT_GAP = voiceInteractGap;
let PROMPT_RAG = promptRag;
let USER_NAME = userName;
let PRODUCT_CODE = productCode;

// 定义文件夹路径
let BASE_FOLDER = "/storage/emulated/0/Download/douyinLive/"
let PRODUCT_FOLDER = BASE_FOLDER + productCode
let PRODUCT_INTRODUCE = PRODUCT_FOLDER + "/introduce.txt"
let PRODUCT_INTERACT = PRODUCT_FOLDER + "/interact.txt"
let INTERACT_FOLDER = files.cwd() + "/interactVoice/";
let INTRODUCE_FOLDER = PRODUCT_FOLDER + "/voice/";
let BACKGROUD_MUSIC_FOLDER = PRODUCT_FOLDER + "/music/";
let PRODUCT_FAQ_TEXT = PRODUCT_FOLDER + "/faqLog.txt";

// 添加礼物感谢词数组
const GIFT_THANKS_GENERAL = [
    "祝哥哥姐天天开心",
    "祝哥哥姐身体健康",
    "祝哥哥姐发大财",
    "祝哥哥姐越来越有钱",
    "祝哥哥姐越来越幸福",
    "祝哥哥姐越来越快乐",
    "祝哥哥姐越来越成功",
    "祝哥哥姐越来越有魅力",
    "祝哥哥姐越来越年轻",
    "祝哥哥姐越来越帅气",
    "祝哥哥姐财源滚滚",
    "祝哥哥姐心想事成",
    "祝哥哥姐身体健康"
];

const GIFT_THANKS_CHAT = [
    "感谢哥哥在寒冷冬夜的陪伴",
    "瑶瑶的心一下子就暖和了",
    "哥真是太好了",
    "有你的支持我很开心",
    "哥哥真好",
    "谢谢你这么支持我",
    "感动，哥哥真好",
    "心动了，瑶瑶要给你加鸡腿",
    "真的非常感谢",
    "谢谢你一直支持我",
    "瑶瑶祝福你",
    "哥哥一定发大财",
    "哥哥真好，谢谢你陪着瑶瑶",
    "好暖啊，谢谢你",
    "希望哥哥天天开心",
    "希望哥哥身体健康",
    "希望哥哥越来越帅",
    "希望哥哥越来越有钱",
    "希望哥哥越来越幸福",
    "希望哥哥越来越快乐",
    "希望哥哥越来越成功",
    "希望哥哥越来越有魅力",
    "希望哥哥越来越年轻",
    "希望哥哥越来越帅气",
    "希望哥哥财源滚滚",
    "希望哥哥心想事成",
    "希望哥哥身体健康"
];

let GIFT_THANKS_WORDS = thanksType == 'chat' ? GIFT_THANKS_CHAT : GIFT_THANKS_GENERAL;

// 全局变量存储prompt
let INTERACT_PROMPT = null;

// 记录上次语音回复的时间
let lastVoiceReplyTime = 0;

// 添加用户交互类型枚举
const INTERACTION_TYPE = {
    COMMENT: 'comment',
    ENTER: 'enter',
    LIKE: 'like',
    GIFT: 'gift'
};

// 修改全局变量，为每种交互类型设置独立的上次回复时间
let lastVoiceReplyTimes = {
    comment: 0,
    enter: 0,
    like: 0,
    gift: 0
};

// 添加全局背景音乐播放器
let backgroundPlayer = null;

// 添加全局变量记录上次点击的商品序号
let lastClickedIndex = 1;  // 默认为1
let isFirstClick = true;   // 添加首次点击标记
let lastProductLinkTime = 0;  // 添加上次商品链接时间记录

// 添加全局常量
const MAX_IDLE_TIME = 60000; // 最大空闲时间(毫秒)

const MAX_PLAY_DURATION = 180000; // 最大播放时间3分钟

// 添加全局API key变量
let OPENAI_API_KEY = null;
let TTS_API_KEY = null;

// 添加全局变量存储CDKey ID
let CDKEY_ID = null;

// 在代码开头添加全局变量声明
let global = {
    isCurrentlyPlaying: false,
    playStartTime: 0,
    errorCount: 0
};

const USERNAME_REGEX = /(.*?)通过.*?来了/;
const TIME_REGEX = /.*(分钟前|刚刚).*/;

// 添加获取API keys的函数
function getApiKeys() {
    try {
        let url = "http://*************:8000/api/get_api_keys";
        
        let res = http.request(url, {
            method: "GET",
            headers: {
                'User-Agent': 'HamibotScript/1.0'
            }
        });

        if (!res || !res.body) {
            console.error("获取API keys失败：无响应");
            return false;
        }

        let result = res.body.json();

        // 解析并存储API keys
        for (let item of result) {
            if (item.service === "openai") {
                OPENAI_API_KEY = item.key;
            } else if (item.service === "tts-max") {
                TTS_API_KEY = item.key;
            }
        }

        // 验证是否都获取到了
        if (!OPENAI_API_KEY || !TTS_API_KEY) {
            console.error("API keys不完整");
            return false;
        }

        return true;
    } catch (e) {
        console.error("获取API keys出错：", e);
        return false;
    }
}

// 验证函数，添加请求体
function validateCDKey() {
    try {
        console.log("开始验证CDKey: " + cdkey);
        
        let url = `http://*************:8000/api/validate_cdkey/js`;
        
        let requestBody = {
            cdkey_key: cdkey,
            user_name: USER_NAME,
            product_code: PRODUCT_CODE,
            voice_sound_id: VOICE_SOUND_ID
        };
        
        let res = http.request(url, {
            method: "POST",
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody),  // 将cdkey、userName、productCode放入请求体
            timeout: 10000  // 添加超时设置
        });

        // 检查响应对象是否存在
        if (!res) {
            console.error("API请求失败：未收到响应");
            toast("CDKey验证失败：网络请求异常");
            sleep(3000);
            return false;
        }

        // 检查响应体是否存在
        if (!res.body) {
            console.error("API响应异常：响应体为空");
            toast("CDKey验证失败：响应数据为空");
            sleep(3000);
            return false;
        }

        try {
            // 尝试解析响应数据
            let result = res.body.json();
            
            // 检查是否包含id字段
            if (result && result.id) {
                console.log("CDKey验证通过");
                CDKEY_ID = result.id;  // 保存CDKey ID
                // CDKey验证通过后获取API keys
                if (!getApiKeys()) {
                    console.error("获取API keys失败");
                    return false;
                }
                return true;
            } else {
                console.error("API响应格式异常：" + JSON.stringify(result));
                toast("CDKey验证失败：响应格式异常");
                sleep(3000);
                return false;
            }
        } catch (parseError) {
            console.error("解析响应数据失败: " + parseError);
            console.error("原始响应数据: " + res.body.string());
            toast("CDKey验证失败：数据解析错误");
            sleep(3000);
            return false;
        }
        
    } catch (e) {
        console.error("CDKey验证出错: " + e);
        console.error("错误堆栈: " + e.stack);
        
        if (e.toString().includes("ECONNREFUSED") || e.toString().includes("Failed to connect")) {
            toast("网络连接失败，请检查网络");
        } else if (e.toString().includes("timeout")) {
            toast("网络请求超时，请稍后重试");
        } else {
            toast("CDKey验证异常: " + e.message);
        }
        
        sleep(3000);
        return false;
    }
}

// 添加更新算力值的函数
function updateTokens(tokens) {
    try {
        if (!CDKEY_ID) {
            console.error("CDKey ID未初始化");
            return false;
        }

        let url = `http://*************:8000/api/update_tokens/${CDKEY_ID}/${tokens}`;
        
        let res = http.request(url, {
            method: "PUT",
            headers: {
                'Content-Type': 'multipart/form-data'
            },
            body: JSON.stringify({})  // 添加空的请求体
        });

        if (!res || !res.body) {
            console.error("更新算力值失败：无响应");
            return false;
        }

        let result = res.body.json();

        if (result.success === true) {
            return true;
        } else {
            console.error("算力值更新失败：" + (result.detail || "未知错误"));
            return false;
        }
    } catch (e) {
        console.error("更新算力值出错：", e);
        return false;
    }
}

// 修改音频播放函数
function playAudio(path, callback) {
    let voicePlayer = null;
    try {
        // 检查文件是否存在
        if (!files.exists(path)) {
            console.error("音频文件不存在：" + path);
            if (callback) callback(new Error("文件不存在"));
            return;
        }
        voicePlayer = new android.media.MediaPlayer();
        voicePlayer.setDataSource(path);
        voicePlayer.prepare();
        voicePlayer.setVolume(1.0, 1.0);
        voicePlayer.start();
        
        global.playStartTime = Date.now();
        global.isCurrentlyPlaying = true;
        
        // 添加错误监听器
        voicePlayer.setOnErrorListener(new android.media.MediaPlayer.OnErrorListener({
            onError: function(mp, what, extra) {
                console.error(`播放错误: what=${what}, extra=${extra}`);
                global.isCurrentlyPlaying = false;
                try {
                    if (voicePlayer) {
                        voicePlayer.reset();
                        voicePlayer.release();
                    }
                } catch(e) {
                    console.error("释放播放器出错：", e);
                }
                if (callback) callback(new Error(`播放错误: ${what}`));
                return true;
            }
        }));

        // 使用定时器检查播放状态
        let checkInterval = setInterval(function() {
            try {
                if (!voicePlayer) {
                    clearInterval(checkInterval);
                    return;
                }

                // 检查是否超时
                if (Date.now() - global.playStartTime > MAX_PLAY_DURATION) {
                    console.log("播放时间超过限制，强制结束播放");
                    clearInterval(checkInterval);
                    global.isCurrentlyPlaying = false;
                    try {
                        voicePlayer.reset();
                        voicePlayer.release();
                        voicePlayer = null;
                    } catch(e) {
                        console.error("释放超时播放器出错：", e);
                    }
                    if (callback) callback();
                    return;
                }

                if (!voicePlayer.isPlaying()) {
                    clearInterval(checkInterval);
                    global.isCurrentlyPlaying = false;
                    try {
                        voicePlayer.reset();
                        voicePlayer.release();
                        voicePlayer = null;
                    } catch(e) {
                        console.error("释放播放器出错：", e);
                    }
                    if (callback) callback();
                }
            } catch(e) {
                clearInterval(checkInterval);
                global.isCurrentlyPlaying = false;
                console.error("播放状态检查出错：", e);
                try {
                    if (voicePlayer) {
                        voicePlayer.reset();
                        voicePlayer.release();
                        voicePlayer = null;
                    }
                } catch(e2) {
                    console.error("释放播放器出错：", e2);
                }
                if (callback) callback(e);
            }
        }, 100);

    } catch(e) {
        global.isCurrentlyPlaying = false;
        console.error("播放初始化出错：", e);
        try {
            if (voicePlayer) {
                voicePlayer.reset();
                voicePlayer.release();
            }
        } catch(e2) {
            console.error("释放播放器出错：", e2);
        }
        if (callback) callback(e);
    }
}

// 评论互动处理
function handleComments() {
    console.log("开始处理评论");
    let history = [];
    const MAX_HISTORY_SIZE = 50;
    
    while(true) {
        try {            
            // 方法1：通过RecyclerView查找
            let recyclerViews = className("androidx.recyclerview.widget.RecyclerView").find();
            // console.log(`找到 ${recyclerViews.length} 个RecyclerView`);

            // 打印每个RecyclerView的信息
            // recyclerViews.forEach((view, index) => {
            //     console.log(`RecyclerView ${index}:`);
            //     console.log("- id: " + view.id());
            //     console.log("- desc: " + view.desc());
            //     console.log("- bounds: " + JSON.stringify(view.bounds()));
                
            //     // 查找其中的TextView
            //     let texts = view.find(className("android.widget.TextView"));
            //     // console.log(`- 包含 ${texts.length} 个TextView`);
            //     texts.forEach((text, i) => {
            //         if (text.text()) {
            //             console.log(`  TextView ${i}: ${text.text()}`);
            //         }
            //     });
            // });

            // 选择第一个包含评论的RecyclerView
            let commentList = null;
            for (let view of recyclerViews) {
                let texts = view.find(className("android.widget.TextView"));
                if (texts.length > 0) {
                    let hasComment = false;
                    for (let text of texts) {
                        let content = text.text();
                        if (content && (content.includes("来了") || content.includes("：") || content.includes(":") || content.includes("送出"))) {
                            hasComment = true;
                            break;
                        }
                    }
                    if (hasComment) {
                        commentList = view;
                        break;
                    }
                }
            }

            if (!commentList) {
                console.log("未找到评论列表容器，等待后重试");
                click(900, 600); // 点击空白防止有弹窗
                sleep(1000);
                continue;
            }
            
            // 直接获取所有TextView
            let commentTexts = commentList.find(className("android.widget.TextView"));
            // console.log(`找到 ${commentTexts.length} 个可能的评论文本`);
            // commentTexts.forEach((textView, index) => {
            //     let desc = textView.desc();
            //     let text = textView.text();
            //     console.log(`TextView ${index}: desc=${desc}, text=${text}`);
            // });

            // 遍历处理每个TextView
            for (let textView of commentTexts) {
                if (!textView) continue;

                let content = textView.text();
                if (!content) continue;

                // 处理评论内容
                content = content.replace(/\u200E/g, "").trim();
                if (!content) continue;

                // 跳过自己发送的消息
                if (content.includes(USER_NAME)) {
                    continue;
                }
                
                // 检查是否是历史评论
                if (history.includes(content)) {
                    continue;
                }

                // 判断是否是时间标记
                if (content.match(TIME_REGEX)) {
                    continue;
                }
                
                // 判断交互类型
                let interactionType = getInteractionType(content);
                let username = extractUsername(content);

                if (!username) continue;
                
                console.log(`发现新交互：${interactionType} - ${content}`);

                // 根据不同类型处理交互
                switch (interactionType) {
                    case INTERACTION_TYPE.COMMENT:
                        let reply = null;
                        if (TEXT_INTERACT_KEY || VOICE_INTERACT_KEY) {
                            reply = generateReply(content);
                        }
                        
                        if (reply && !reply.trim().includes("请稍等")) {
                            if (TEXT_INTERACT_KEY) {
                                // console.log(`准备发送文字回复: ${reply}`);
                                sendReply(textView.parent(), reply);
                            }
                            if (VOICE_INTERACT_KEY) {
                                let ttsText = username + "，" + reply;
                                // console.log(`准备发送语音回复: ${ttsText}`);
                                sendVoiceReply(ttsText, INTERACTION_TYPE.COMMENT);
                            }
                        }
                        break;
                        
                    case INTERACTION_TYPE.ENTER:
                        if (VOICE_INTERACT_KEY) {
                            let ttsText = `欢迎${username}进入我的直播间`;
                            sendVoiceReply(ttsText, INTERACTION_TYPE.ENTER);
                        }
                        break;
                    case INTERACTION_TYPE.GIFT:
                        if (VOICE_INTERACT_KEY) {
                            let giftName = null;
                            try {
                                giftName = content.split("送出")[1].split("x")[0].replace("*", "").trim();
                            } catch (e) {
                                giftName = "礼物";
                            }
                            let randomThanks = GIFT_THANKS_WORDS[Math.floor(Math.random() * GIFT_THANKS_WORDS.length)];
                            let ttsText = `感谢${username}送的${giftName}，${randomThanks}`;
                            sendVoiceReply(ttsText, INTERACTION_TYPE.GIFT);
                        }
                        break;
                }
                
                // 记录已处理的交互
                history.push(content);
                if (history.length > MAX_HISTORY_SIZE) {
                    history.shift();
                }
            }

            sleep(1000);

        } catch(e) {
            console.error("评论处理出错：", e);
            console.error("错误堆栈：", e.stack);
            sleep(5000);
        }
    }
}

// 生成回复内容
function generateReply(content) {
    try {
        // 先更新算力值
        if (!updateTokens(1)) {  // generateReply固定消耗1算力
            console.error("算力值更新失败，跳过回复生成");
            return null;
        }

        if (!OPENAI_API_KEY) {
            console.error("OpenAI API key未初始化");
            return null;
        }
        
        // 使用全局prompt变量
        if (!INTERACT_PROMPT) {
            console.error("prompt未初始化");
            return null;
        }
        
        // 替换内容
        let prompt = INTERACT_PROMPT.replace(/{{content}}/g, content);

        // 准备API请求
        let url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
        let headers = {
            "Authorization": "Bearer " + OPENAI_API_KEY,
            "Content-Type": "application/json; charset=utf-8"
        };
        let requestBody = {
            "model": "qwen-turbo",
            "messages": [
                {
                    "role": "system",
                    "content": "你是基于大语言模型的AI智能助手，旨在回答并解决人们的任何问题"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        };

        // 发送请求
        let res = http.request(url, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(requestBody),
            timeout: 10000
        });

        if (res.statusCode === 200) {
            let result = res.body.json();
            if (result && result.choices && result.choices[0] && result.choices[0].message) {
                let reply = result.choices[0].message.content;

                // 记录content和reply到文件
                try {
                    let logEntry = `Date: ${new Date(new Date().setHours(new Date().getHours() + 8)).toISOString()}\nContent: ${content}\nReply: ${reply}\n\n`;
                    files.append(PRODUCT_FAQ_TEXT, logEntry);
                } catch (e) {
                    console.error("记录FAQ日志失败：", e);
                }

                return reply;
            } else {
                console.error("API响应格式错误：" + JSON.stringify(result));
                return null;
            }
        } else {
            console.error("API请求失败：" + res.statusCode + " - " + res.body.string());
            return null;
        }

    } catch (e) {
        console.error("生成回复出错：", e);
        return null;
    }
}
// 在发送回复前分析结构
function sendReply(commentItem, content) {
    try {
        // 查找评论文本元素
        let textView = commentItem.findOne(className("android.widget.TextView"));
        if (!textView) {
            console.log("未找到评论文本元素");
            // 点击退出
            click(900, 600);
            return;
        }
        
        // 直接点击文本元素
        textView.click();
        sleep(1000);

        // 查找可点击的"回复TA"按钮
        let menuItems = className("android.widget.LinearLayout").find();
        let found = false;
        for (let item of menuItems) {
            if (item.clickable() && item.findOne(text("回复TA"))) {
                item.click();
                found = true;
                sleep(1000);
                break;
            }
        }
        
        if (!found) {
            console.log("未找到回复TA按钮");
            // 点击退出
            click(900, 600);
            return;
        }

        // 查找输入框
        let inputBox = className("android.widget.EditText").findOne(2000);
        if (!inputBox) {
            console.log("未找到输入框");
            // 点击退出
            click(900, 600);
            return;
        }

        // 在原有内容后追加回复内容
        inputBox.click();  // 先点击输入框
        sleep(500);
        setClip(content);  // 置剪贴板内容
        sleep(500);
        paste();  // 粘贴内容
        sleep(1000);

        // 点击发送
        let sendBtn = text("发送").findOne(2000);
        if (!sendBtn) {
            console.log("未找到发送按钮");
            // 点击退出
            click(900, 600);
            return;
        }
        sendBtn.click();
        sleep(1000);

    } catch(e) {
        console.error("发送回复失败：", e);
        // 发生任何错误时也点击退出
        sleep(1000);
        click(900, 600);
    }
}

function sendVoiceReply(text, interactionType) {
    try {
        // 检查是否启用了该类型的语音互动
        if (!allVoiceInteractType.includes(interactionType)) {
            console.log(`${interactionType}类型的语音互动未启用，跳过回复`);
            return;
        }
        
        // 根据交互类型检查冷却时间
        let now = Date.now();
        if (now - lastVoiceReplyTimes[interactionType] < VOICE_INTERACT_GAP * 1000) {
            console.log(`${interactionType}语音回复冷却中，跳过回复`);
            return;
        }
      
        console.log("开始生成语音回复: " + text);
        
        // 计算语音文本的算力消耗（每字符0.05算力）
        let tokens = Math.ceil(text.length * 0.05);
        
        // 先更新算力值
        if (!updateTokens(tokens)) {
            console.error("算力值更新失败，跳过语音生成");
            return;
        }

        if (!TTS_API_KEY) {
            console.error("TTS API key未初始化");
            return;
        }

        // 准备API请求
        let url = "https://api.minimax.chat/v1/t2a_v2?GroupId=1836702808724738844";
        let headers = {
            "Authorization": "Bearer " + TTS_API_KEY,
            "Content-Type": "application/json"
        };
        let requestBody = {
            "model": "speech-01-turbo",
            "text": text,
            "stream": false,
            "voice_setting": {
                "voice_id": VOICE_SOUND_ID || "ttv-voice-2024112722551824-IG1c5u0Q",
                "speed": 1.1,
                "vol": 1,
                "pitch": 0
            }
        };

        // 发送请求
        let res = http.request(url, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(requestBody)
        });

        if (res.statusCode === 200) {
            let result = res.body.json();
            if (result.base_resp.status_code === 0) {
                let audioData = result.data.audio;
                let fileName = "voice_" + Date.now() + ".mp3";
                let filePath = INTERACT_FOLDER + fileName;
                
                // 修改解码方式，确保byte值在正确范围内
                let bytes = [];
                for (let i = 0; i < audioData.length; i += 2) {
                    let value = parseInt(audioData.substr(i, 2), 16);
                    // 将无符号字节(0-255)转换为有符号字节(-128到127)
                    bytes.push(value > 127 ? value - 256 : value);
                }
                
                let byteArray = java.lang.reflect.Array.newInstance(java.lang.Byte.TYPE, bytes.length);
                for (let i = 0; i < bytes.length; i++) {
                    byteArray[i] = bytes[i];
                }
                
                files.writeBytes(filePath, byteArray);
                lastVoiceReplyTimes[interactionType] = Date.now();
            } else {
                console.error("TTS API响应错误：" + JSON.stringify(result.base_resp));
            }
        } else {
            console.error("TTS API请求失败：" + res.statusCode);
            console.error("错误响应：" + res.body.string());
        }

    } catch (e) {
        console.error("生成语音回复出错：", e.stack);
    }
}

// 语音播放处理
function handleVoice() {
    let isRunning = true;
    let lastPlayTime = Date.now();
    let currentPlayer = null;

    // 清理播放器资源
    function cleanup() {
        if (currentPlayer) {
            try {
                currentPlayer.reset();
                currentPlayer.release();
                currentPlayer = null;
            } catch(e) {
                console.error("清理播放器失败:", e);
            }
        }
    }

    // 播放单个音频文件
    function playAudio(path, callback) {
        try {
            // 检查文件是否存在
            if (!files.exists(path)) {
                console.error("音频文件不存在：" + path);
                if (callback) callback(new Error("文件不存在"));
                return;
            }

            cleanup(); // 清理之前的播放器
            
            currentPlayer = new android.media.MediaPlayer();
            currentPlayer.setDataSource(path);
            currentPlayer.prepare();
            currentPlayer.setVolume(1.0, 1.0);
            currentPlayer.start();
            
            global.playStartTime = Date.now();
            global.isCurrentlyPlaying = true;
            
            // 添加错误监听器
            currentPlayer.setOnErrorListener(new android.media.MediaPlayer.OnErrorListener({
                onError: function(mp, what, extra) {
                    console.error(`播放错误: what=${what}, extra=${extra}`);
                    global.isCurrentlyPlaying = false;
                    cleanup();
                    if (callback) callback(new Error(`播放错误: ${what}`));
                    return true;
                }
            }));

            // 使用定时器检查播放状态
            let checkInterval = setInterval(function() {
                try {
                    if (!currentPlayer) {
                        clearInterval(checkInterval);
                        return;
                    }

                    // 检查是否超时
                    if (Date.now() - global.playStartTime > MAX_PLAY_DURATION) {
                        console.log("播放时间超过限制，强制结束播放");
                        clearInterval(checkInterval);
                        global.isCurrentlyPlaying = false;
                        cleanup();
                        if (callback) callback();
                        return;
                    }

                    if (!currentPlayer.isPlaying()) {
                        clearInterval(checkInterval);
                        global.isCurrentlyPlaying = false;
                        cleanup();
                        if (callback) callback();
                    }
                } catch(e) {
                    clearInterval(checkInterval);
                    global.isCurrentlyPlaying = false;
                    console.error("播放状态检查出错：", e);
                    cleanup();
                    if (callback) callback(e);
                }
            }, 100);

        } catch(e) {
            global.isCurrentlyPlaying = false;
            console.error("播放初始化出错：", e);
            cleanup();
            if (callback) callback(e);
        }
    }

    // 播放下一个音频
    function playNext() {
        if (!isRunning) return;
        
        try {
            // 优先处理互动语音文件
            let interactFiles = java.io.File(INTERACT_FOLDER).list();
            if (interactFiles && interactFiles.length > 0) {
                interactFiles = interactFiles.filter(name => name.endsWith('.mp3') || name.endsWith('.aac'));
                
                if(interactFiles.length > 0) {
                    let path = INTERACT_FOLDER + interactFiles[0];
                    console.log("准备播放互动语音：" + interactFiles[0]);
                    
                    playAudio(path, function(error) {
                        if (error) {
                            console.error("播放互动语音出错", error);
                            sleep(1000);
                            playNext();
                        } else {
                            console.log("播放完成，准备删除文件：" + path);
                            sleep(500);
                            
                            // 尝试删除文件
                            try {
                                files.remove(path);
                            } catch(e) {
                                console.error("删除文件失败：", e);
                            }
                            
                            sleep(1000);
                            playNext();
                        }
                    });
                    return;
                }
            }

            // 处理商品介绍语音文件
            let introduceFiles = java.io.File(INTRODUCE_FOLDER).list();
            if (introduceFiles && introduceFiles.length > 0) {
                introduceFiles = introduceFiles.filter(name => name.endsWith('.mp3') || name.endsWith('.aac'));
                
                if(introduceFiles.length > 0) {
                    let randomIndex = Math.floor(Math.random() * introduceFiles.length);
                    let fileName = introduceFiles[randomIndex];
                    let path = INTRODUCE_FOLDER + fileName;
                    
                    if (!files.exists(path)) {
                        console.error("文件不存在：" + path);
                        sleep(1000);
                        playNext();
                        return;
                    }
                    
                    let productKey = null;
                    let linkMatch = fileName.replace('.mp3', '').match(/_link(\d+)/);
                    if (linkMatch) {
                        productKey = linkMatch[1];
                    }

                    console.log("播放商品介绍：" + fileName);
                    
                    playAudio(path, function(error) {
                        if (error) {
                            console.error("播放介绍语音出错：", error);
                            sleep(1000);
                            playNext();
                        } else {
                            if (productKey && popLink !== 'n') {
                                threads.start(function() {
                                    try {
                                        let now = Date.now();
                                        if (now - lastProductLinkTime >= (popLinkGap * 1000)) {
                                            handleProductLink(productKey);
                                        }
                                    } catch(e) {
                                        console.error("处理商品链接失败：", e);
                                    }
                                });
                            }
                            
                            sleep(1000);
                            playNext();
                        }
                    });
                    return;
                }
            }

            // 没有文件可播放时等待
            console.log("当前没有可播放的文件，等待5秒后重试");
            sleep(5000);
            playNext();

        } catch(e) {
            console.error("播放循环出错：", e);
            sleep(3000);
            playNext();
        }
    }

    // 启动播放循环
    threads.start(function() {
        playNext();
    });

    // 返回控制接口
    return {
        stop: function() {
            isRunning = false;
            cleanup();
        },
        restart: function() {
            isRunning = false;
            cleanup();
            sleep(1000);
            isRunning = true;
            playNext();
        },
        getLastPlayTime: function() {
            return lastPlayTime;
        },
        cleanup: cleanup
    };
}

// 处理商品链接的函数
function handleProductLink(productKey) {
    try {
        console.log("开始处理商品链接，序号：" + productKey);
        
        // 根据popLink类型查找不同的按钮
        let targetText = popLink === 'group' ? "团购" : "商品";
        let viewGroups = className("android.view.ViewGroup").find();
        
        let targetViewGroup = null;
        for (let group of viewGroups) {
            let textView = group.findOne(className("android.widget.TextView").text(targetText));
            if (textView) {
                // 从团购文本开始向上查找合适的可点击容器
                let currentParent = textView.parent();
                let maxTries = 3;
                let tries = 0;
                
                while (currentParent && tries < maxTries) {
                    if (currentParent.clickable() && currentParent.bounds().height() < 300) {
                        targetViewGroup = currentParent;
                        break;
                    }
                    currentParent = currentParent.parent();
                    tries++;
                }
                
                if (targetViewGroup) {
                    // 点击打开弹窗
                    let bounds = targetViewGroup.bounds();
                    click(bounds.centerX(), bounds.centerY());
                    
                    // 等待弹窗加载
                    sleep(3000);
                    
                    // 如果是第一次点击，先重置位置
                    if (isFirstClick) {
                        console.log("首次点击，重置商品列表位置");
                        // 快速向下滑动5次，确保回到顶部
                        for (let i = 0; i < 5; i++) {
                            swipe(540, 1000, 540, 1500, 300);
                            sleep(500);
                        }
                        isFirstClick = false;
                        lastClickedIndex = 1;
                    }
                    
                    // 获取当前要点击的商品序号
                    let currentIndex = parseInt(productKey);
                    
                    // 计算需要滑动的次数和方向
                    let scrollDistance = currentIndex - lastClickedIndex;
                    
                    if (scrollDistance !== 0) {
                        // 根据距离决定滑动方向和次数
                        let scrollTimes = Math.abs(scrollDistance);
                        let isScrollUp = scrollDistance > 0;
                        
                        for (let i = 0; i < scrollTimes; i++) {
                            if (isScrollUp) {
                                swipe(540, 1500, 540, 1000, 500);
                            } else {
                                swipe(540, 1000, 540, 1500, 500);
                            }
                            sleep(1000);
                        }
                    }
                    
                    // 点击讲解按钮
                    click(950, 1510);
                    
                    // 更新状态
                    lastClickedIndex = currentIndex;
                    lastProductLinkTime = Date.now();
                    
                    // 等待点击效果
                    sleep(1500);
                    
                    // 点击退出弹窗
                    click(900, 600);
                    break;
                }
            }
        }
        
        if (!targetViewGroup) {
            console.log("未找到正确的团购按钮组件");
        }
        
    } catch(e) {
        console.error("处理商品链接失败：", e);
        sleep(1000);
        click(900, 600); // 错误后点击退出弹窗
    }
}

// 创建必要的文件夹
function initFolders() {
    try {
        // 确保私有目录存在
        let interactDir = new java.io.File(INTERACT_FOLDER);
        if (!interactDir.exists()) {
            interactDir.mkdirs();
            console.log("创建互动语音临时文件夹：" + INTERACT_FOLDER);
        }
        
        // 外部存储的目录
        let introduceDir = new java.io.File(INTRODUCE_FOLDER);
        if (!introduceDir.exists()) {
            introduceDir.mkdirs();
            console.log("创建商品话术文件夹：" + INTRODUCE_FOLDER);
        }
    } catch(e) {
        console.error("创建文件夹失败：", e);
    }
}

// 提取用户名函数
function extractUsername(content) {
    try {
        let username = "";
        // 处理不同格式的评论
        if (content.includes("送出")) {
            username = content.split("送出")[0];
        } else if (content.includes("：")) {
            username = content.split("：")[0];
        } else if (content.includes(":")) {  // 处理英文冒号
            username = content.split(":")[0];
        } else if (content.includes("为你点赞了")) {
            username = content.split("为你点赞了")[0];
        } else {
            // 使用正则匹配"通过xxx来了"的格式
            let match = content.match(USERNAME_REGEX);
            if (match) {
                username = match[1];
            } else if (content.includes("来了")) {
                username = content.split("来了")[0];
            }
        }
        
        // 清理用户名
        return username.replace('*', '').trim();
    } catch(e) {
        console.error("提取用户名出错：", e);
        return "";
    }
}

// 初始化函数中添加prompt加载
function initSystem() {
    console.log("初始化系统...");
    
    // 初始化文件夹
    initFolders();
    
    // 加载prompt
    try {
        // 优先检查PRODUCT_INTERACT文件
        if(files.exists(PRODUCT_INTERACT)) {
            INTERACT_PROMPT = files.read(PRODUCT_INTERACT);
            console.log("使用互动话术文件作为prompt");
        } else {
            let promptText = `###任务：
你是抖音直播间的控场助理，请根据现在正在直播的播报内容，回复用户的评论，做到简洁明了，不要错误回答，回了不了的问题回复 请稍等。

###播报内容：
{{introduce}}

###用户评论：{{content}}

###注意：
1. 回复内容一定要简洁，亲切，不要超过30个字。
2. 输出内容为直接回复客户的内容，不能重复用户问题，不需要包含自己的角色。

直接输出回复客户的内容：`;
            let introduceText = files.read(PRODUCT_INTRODUCE);
            if (!introduceText) {
                console.error("未找到商品直播稿文件：" + PRODUCT_INTRODUCE);
                exit();
            }
            INTERACT_PROMPT = promptText.replace(/{{introduce}}/g, introduceText);
        }
        if (PROMPT_RAG) {
            INTERACT_PROMPT = "###补充知识：\n" + PROMPT_RAG + "\n\n" + INTERACT_PROMPT;
        }
    } catch(e) {
        console.error("加载商品介绍失败：", e);
        exit();
    }
}

// 添加判断交互类型的函数
function getInteractionType(content) {
    if (content.includes("送出")) {
        return INTERACTION_TYPE.GIFT;
    } else if (content.includes("：") || content.includes(":")) {
        return INTERACTION_TYPE.COMMENT;
    } else if (content.includes("为你点赞了")) {
        return INTERACTION_TYPE.LIKE;
    } else if (content.match(/(.*?)通过.*?来了/) || content.includes("来了")) {
        return INTERACTION_TYPE.ENTER;
    }
    return null;
}

// 添加背景音乐处理函数
function handleBackgroundMusic() {
    let isPlaying = true; // 添加播放状态标志

    function playBackgroundMusic() {
        while (isPlaying) {
            try {
                console.log("开始检查背景音乐文件夹...");
                let musicFiles = java.io.File(BACKGROUD_MUSIC_FOLDER).list();
                if (!musicFiles || musicFiles.length === 0) {
                    console.log("背景音乐文件夹为空");
                    sleep(5000);
                    continue;
                }

                musicFiles = musicFiles.filter(function(name) {
                    return name.endsWith('.mp3');
                });

                if (musicFiles.length === 0) {
                    console.log("没有找到mp3文件");
                    sleep(5000);
                    continue;
                }

                // 随机选择一首音乐播放
                let randomIndex = Math.floor(Math.random() * musicFiles.length);
                let musicPath = BACKGROUD_MUSIC_FOLDER + musicFiles[randomIndex];
                console.log("播放背景音乐：" + musicFiles[randomIndex]);

                // 创建新的播放器实例
                let player = new android.media.MediaPlayer();
                
                try {
                    player.setDataSource(musicPath);
                    player.prepare();
                    player.setVolume(0.05, 0.05);
                    player.start();

                    // 等待播放完成
                    while (player.isPlaying()) {
                        sleep(1000);
                    }
                } catch(e) {
                    console.error("音乐播放出错：", e);
                } finally {
                    try {
                        player.reset();
                        player.release();
                    } catch(e) {
                        console.error("释放播放器出错：", e);
                    }
                }

                sleep(1000); // 播放间隔
                
            } catch(e) {
                console.error("背景音乐循环出错：", e);
                sleep(3000);
            }
        }
    }

    // 启动背景音乐循环
    threads.start(function() {
        playBackgroundMusic();
    });

    // 添加停止播放的方法（如果需要）
    return {
        stop: function() {
            isPlaying = false;
        }
    };
}

function main() {
    try {
        // 1. 初始化基础设置
        console.show();
        console.setPosition(0, 100);
        console.log("=== 直播助手启动 ===");
        console.log("时间：" + new Date().toLocaleString());
        
        // 2. 检查权限：requestPermissions有问题，发布会报错
        // checkAndRequestPermissions();
        
        // 3. CDKey验证
        console.log("验证CDKey...");
        if (!validateCDKey()) {
            console.error("CDKey验证失败，脚本退出");
            toast("CDKey验证失败，请检查配置");
            sleep(3000);
            exit();
        }
        console.log("CDKey验证成功");

        // 4. 系统初始化
        console.log("初始化系统...");
        initSystem();
        
        // 5. 创建控制器
        let controllers = initControllers();
        
        // 6. 启动监控
        initMonitors(controllers);
        
        // 7. 保持脚本运行
        keepAlive();
        
    } catch(e) {
        console.error("main错误：" + e);
    } finally {
        // 确保清理资源
        if (controllers) {
            if (controllers.voice) {
                controllers.voice.cleanup();
            }
            if (controllers.backgroundMusic) {
                controllers.backgroundMusic.stop();
            }
        }
    }
}

// // 权限检查和请求
// function checkAndRequestPermissions() {
//     console.log("检查权限...");
//     let permissions = [
//         "android.permission.WRITE_EXTERNAL_STORAGE",
//         "android.permission.READ_EXTERNAL_STORAGE"
//     ];

//     // 检查是否已经拥有所需的权限
//     let needRequestPermissions = [];
//     for (let permission of permissions) {
//         if (!checkPermission(permission)) {
//             needRequestPermissions.push(permission);
//         }
//     }

//     // 如果有需要请求的权限
//     if (needRequestPermissions.length > 0) {
//         console.log("请求权限：" + needRequestPermissions.join(", "));
//         let runtime = engines.myEngine().runtime;
//         try {
//             let granted = runtime.requestPermissions(needRequestPermissions);
//             if (!granted) {
//                 throw new Error("必要权限未授予：" + needRequestPermissions.join(", "));
//             }
//         } catch(e) {
//             console.error("请求权限失败：" + e);
//             throw e;
//         }
//     }
    
//     console.log("权限检查完成");
// }

// // 检查单个权限是否已获取
// function checkPermission(permission) {
//     let context = context || runtime.getContext();
//     let result = context.checkSelfPermission(permission);
//     return result === android.content.pm.PackageManager.PERMISSION_GRANTED;
// }

// 初始化控制器
function initControllers() {
    console.log("初始化控制器...");
    let controllers = {
        voice: null,
        backgroundMusic: null,
        comment: null
    };

    // 1. 启动语音控制器
    try {
        controllers.voice = handleVoice();
        console.log("语音控制器启动成功");
    } catch(e) {
        console.error("语音控制器启动失败：", e);
        throw e;
    }

    // 2. 启动背景音乐控制器
    try {
        controllers.backgroundMusic = handleBackgroundMusic();
        console.log("背景音乐控制器启动成功");
    } catch(e) {
        console.error("背景音乐控制器启动失败：", e);
        // 背景音乐失败不影响主要功能，继续运行
    }

    // 3. 启动评论处理
    if (TEXT_INTERACT_KEY || VOICE_INTERACT_KEY) {
        try {
            controllers.comment = threads.start(function() {
                handleComments();
            });
            console.log("评论处理器启动成功");
        } catch(e) {
            console.error("评论处理器启动失败：", e);
            throw e;
        }
    }

    return controllers;
}

// 初始化监控系统
function initMonitors(controllers) {
    console.log("初始化监控系统...");
    
    // 1. 语音系统监控
    threads.start(function() {
        let consecutiveErrors = 0;
        const MAX_CONSECUTIVE_ERRORS = 3;
        
        while(true) {
            try {
                sleep(15000); // 每15秒检查一次
                
                let now = Date.now();
                let lastPlay = controllers.voice.getLastPlayTime();
                
                // 检查播放状态
                if (global.isCurrentlyPlaying && now - global.playStartTime > MAX_PLAY_DURATION) {
                    console.log("检测到播放卡住，强制重置");
                    global.isCurrentlyPlaying = false;
                    global.playStartTime = 0;
                    consecutiveErrors++;
                } else if (!global.isCurrentlyPlaying && now - lastPlay > MAX_IDLE_TIME) {
                    console.log("检测到异常空闲状态");
                    consecutiveErrors++;
                } else {
                    consecutiveErrors = 0; // 重置错误计数
                }

                // 如果连续错误超过阈值，强制重启
                if (consecutiveErrors >= MAX_CONSECUTIVE_ERRORS) {
                    console.log("检测到持续异常，执行强制重启");
                    controllers.voice.restart();
                    consecutiveErrors = 0;
                    sleep(5000);
                }
                
            } catch(e) {
                console.error("语音监控检查出错：", e);
                sleep(5000);
            }
        }
    });

    // 3. 系统状态监控
    threads.start(function() {
        let lastCheckTime = Date.now();
        
        while(true) {
            try {
                sleep(30000); // 每30秒检查一次
                
                // 检查系统时间是否正常流动
                let now = Date.now();
                if (now - lastCheckTime > 60000) { // 如果两次检查间隔超过1分钟
                    console.log("检测到系统时间异常，重新初始化控制器");
                    controllers.voice.restart();
                }
                lastCheckTime = now;
                
                // 检查文件系统
                checkFileSystem();
                
            } catch(e) {
                console.error("系统状态监控出错：", e);
            }
        }
    });
}

// 文件系统检查
function checkFileSystem() {
    try {
        // 检查必要的文件夹
        let folders = [
            INTERACT_FOLDER,
            INTRODUCE_FOLDER,
            BACKGROUD_MUSIC_FOLDER
        ];

        for (let folder of folders) {
            if (!files.exists(folder)) {
                console.log("重建文件夹：" + folder);
                files.createWithDirs(folder);
            }
        }

        // 清理过期的临时文件
        let now = Date.now();
        let tempFiles = files.listDir(INTERACT_FOLDER);
        for (let file of tempFiles) {
            let filePath = INTERACT_FOLDER + file;
            let fileInfo = files.getInfo(filePath);
            if (now - fileInfo.lastModified > 3600000) { // 1小时以上的临时文件
                files.remove(filePath);
            }
        }
    } catch(e) {
        console.error("文件系统检查出错：", e);
    }
}

// 保持脚本运行
function keepAlive() {
    console.log("脚本运行中...");
    
    // 注册按键监听
    events.observeKey();
    events.on("key", function(keyCode, event) {
        if (keyCode === keys.volume_down) {
            console.log("检测到音量减键，准备退出脚本");
            exit();
        }
    });

    // 注册退出处理
    events.on("exit", function() {
        console.log("=== 脚本退出 ===");
        console.log("时间：" + new Date().toLocaleString());
        // 清理资源
        try {
            global.isCurrentlyPlaying = false;
            threads.shutDownAll();
        } catch(e) {
            console.error("退出清理失败：", e);
        }
    });

    // 保持运行
    while(true) {
        sleep(1000);
    }
}

// 启动主函数
main();