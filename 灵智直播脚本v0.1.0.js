// 基础设置
console.show();
toastLog("开始运行");

const { userName, productCode, promptRag, textInteract, voiceInteract, voiceSoundId, voiceInteractGap } = hamibot.env;

let TEXT_INTERACT_KEY = textInteract == 'y';
let VOICE_INTERACT_KEY = voiceInteract == 'y';
let VOICE_SOUND_ID = voiceSoundId;
let VOICE_INTERACT_GAP = voiceInteractGap;
let PROMPT_RAG = promptRag;
let USER_NAME = userName;

// 定义文件夹路径
let BASE_FOLDER = "/storage/emulated/0/Download/douyinLive/resource/"
let PRODUCT_FOLDER = BASE_FOLDER + productCode
let PRODUCT_INTRODUCE = PRODUCT_FOLDER + "/introduce.txt"
let INTERACT_FOLDER = files.cwd() + "/interactVoice/";
let INTRODUCE_FOLDER = PRODUCT_FOLDER + "/voice/";

// 全局变量存储prompt
let INTERACT_PROMPT = null;

// 记录上次语音回复的时间
let lastVoiceReplyTime = 0;

// 添加用户交互类型枚举
const INTERACTION_TYPE = {
    COMMENT: 'comment',
    ENTER: 'enter',
    LIKE: 'like'
};

// 音频播放函数
function playAudio(path, callback) {
    let player = new android.media.MediaPlayer();
    try {
        player.setDataSource(path);
        player.prepare();
        player.start();
        
        // 获取音频时长
        let duration = player.getDuration();
        
        // 使用定时器检查播放状态
        let checkInterval = setInterval(function() {
            try {
                if (!player.isPlaying()) {
                    clearInterval(checkInterval);
                    try {
                        player.reset();
                        player.release();
                        if (callback) {
                            callback();
                        }
                    } catch(e) {
                        console.error("释放播放器出错：", e);
                        if (callback) {
                            callback(e);
                        }
                    }
                }
            } catch(e) {
                clearInterval(checkInterval);
                console.error("播放状态检查出错：", e);
                try {
                    player.reset();
                    player.release();
                } catch(e2) {
                    console.error("释放播放器出错：", e2);
                }
                if (callback) {
                    callback(e);
                }
            }
        }, 100);
        
    } catch(e) {
        console.error("播放初始化出错：", e);
        try {
            player.reset();
            player.release();
        } catch(e2) {
            console.error("释放播放器出错：", e2);
        }
        if (callback) {
            callback(e);
        }
    }
}

// 评论互动处理
function handleComments() {
    console.log("开始处理评论");
    let history = [];
    const MAX_HISTORY_SIZE = 50;
    
    while(true) {
        try {
            console.log("开始新一轮评论检查...");
            
            // 查找评论列表
            let commentList = className("androidx.recyclerview.widget.RecyclerView").findOne(2000);
            if (!commentList) {
                console.log("未找到评论列表，继续等待...");
                sleep(1000);
                continue;
            }

            // 获取所有评论项
            let comments = commentList.children();
            if (comments.length === 0) {
                console.log("暂无评论");
                sleep(1000);
                continue;
            }

            console.log("找到评论数量：" + comments.length);

            // 遍历评论
            for (let i = 0; i < comments.length; i++) {
                let commentItem = comments[i];
                if (!commentItem) continue;

                let textView = commentItem.findOne(className("android.widget.TextView"));
                if (!textView) continue;

                let content = textView.text();
                if (!content) {
                    content = commentItem.desc();
                }
                if (!content) continue;

                // 处理评论内容
                content = content.replace(/\u200E/g, "").trim();

                // 跳过自己发送的消息
                if (content.includes(USER_NAME)) {
                    continue;
                }
                
                // 判断交互类型
                let interactionType = getInteractionType(content);
                let username = extractUsername(content);
                
                // 检查是否是历史评论
                if (history.includes(content)) {
                    continue;
                }

                // 获取评论时间
                let timeView = commentItem.findOne(className("android.widget.TextView").textMatches(/.*(分钟前|刚刚).*/));
                if (timeView) {
                    let timeText = timeView.text();
                    // 只处理"刚刚"或"1分钟前"的评论
                    if (!timeText.includes("刚刚") && !timeText.includes("1分钟前")) {
                        history.push(content);
                        continue;
                    }
                } else {
                    console.log("未找到时间信息，默认为新评论");
                }

                if (!username) continue;
                
                console.log(`发现新交互：${interactionType} - ${content}`);

                // 根据不同类型处理交互
                switch (interactionType) {
                    case INTERACTION_TYPE.COMMENT:
                        let reply = null;
                        if (TEXT_INTERACT_KEY || VOICE_INTERACT_KEY) {
                            reply = generateReply(content);
                        }
                        
                        if (reply && reply.trim() !== "请稍后") {
                            if (TEXT_INTERACT_KEY) {
                                sendReply(commentItem, reply);
                            }
                            if (VOICE_INTERACT_KEY) {
                                let ttsText = username + "宝子，" + reply;
                                sendVoiceReply(ttsText);
                            }
                        }
                        break;
                        
                    case INTERACTION_TYPE.ENTER:
                        if (VOICE_INTERACT_KEY) {
                            let ttsText = `欢迎${username}进入我的直播间`;
                            sendVoiceReply(ttsText);
                        }
                        break;
                }
                
                // 记录已处理的交互
                history.push(content);

                // 如果超出大小限制，删除最早的记录
                if (history.length > MAX_HISTORY_SIZE) {
                    history.shift();  // 删除数组第一个元素
                }
            }

            sleep(5000);

        } catch(e) {
            console.error("评论处理出错：", e);
            sleep(5000);
        }
    }
}
// 生成回复内容
function generateReply(content) {
    try {
        // 使用全局prompt变量
        if (!INTERACT_PROMPT) {
            console.error("prompt未初始化");
            return null;
        }
        
        // 替换内容
        let prompt = INTERACT_PROMPT.replace(/{{content}}/g, content);

        // 准备API请求
        let url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions";
        let headers = {
            "Authorization": "Bearer sk-4240848aa3314a2e8bef1c762bbfb305",
            "Content-Type": "application/json; charset=utf-8"
        };
        let requestBody = {
            "model": "qwen-turbo",
            "messages": [
                {
                    "role": "system",
                    "content": "你是基于大语言模型的AI智能助手，旨在回答并解决人们的任何问题"
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        };

        // 发送请求
        console.log("发送API请求...");
        let res = http.request(url, {
            method: "POST",
            headers: {
                'Authorization': 'Bearer sk-4240848aa3314a2e8bef1c762bbfb305',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestBody),
            timeout: 10000
        });

        if (res.statusCode === 200) {
            let result = res.body.json();
            if (result && result.choices && result.choices[0] && result.choices[0].message) {
                let reply = result.choices[0].message.content;
                return reply;
            } else {
                console.error("API响应格式错误：" + JSON.stringify(result));
                return null;
            }
        } else {
            console.error("API请求失败：" + res.statusCode + " - " + res.body.string());
            return null;
        }

    } catch (e) {
        console.error("生成回复出错：", e);
        return null;
    }
}
// 在发送回复前分析结构
function sendReply(commentItem, content) {
    try {
        // 查找评论文本元素
        let textView = commentItem.findOne(className("android.widget.TextView"));
        if (!textView) {
            console.log("未找到评论文本元素");
            return;
        }
        
        // 直接点击文本元素
        textView.click();
        sleep(1000);

        // 查找可点击的"回复TA"按钮
        let menuItems = className("android.widget.LinearLayout").find();
        for (let item of menuItems) {
            if (item.clickable() && item.findOne(text("回复TA"))) {
                item.click();
                sleep(1000);
                break;
            }
        }

        // 查找输入框
        let inputBox = className("android.widget.EditText").findOne(2000);
        if (!inputBox) {
            console.log("未找到输入框");
            return;
        }

        // 在原有内容后追加回复内容
        inputBox.click();  // 先点击输入框
        sleep(500);
        setClip(content);  // 设置剪贴板内容
        sleep(500);
        paste();  // 粘贴内容
        sleep(1000);

        // 点击发送
        let sendBtn = text("发送").findOne(2000);
        if (!sendBtn) {
            console.log("未找到发送按钮");
            return;
        }
        sendBtn.click();
        sleep(1000);

    } catch(e) {
        console.error("发送回复失败：", e);
    }
}

function sendVoiceReply(text) {
    try {
        // 检查是否在冷却时间内
        let now = Date.now();
        if (now - lastVoiceReplyTime < VOICE_INTERACT_GAP * 1000) {
            console.log("语音回复冷却中，跳过回复");
            return;
        }

        // 准备API请求
        let url = "https://api.minimax.chat/v1/t2a_v2?GroupId=1836702808724738844";
        let headers = {
            "Authorization": "Bearer ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
            "Content-Type": "application/json"
        };
        let requestBody = {
            "model": "speech-01-turbo",
            "text": text,
            "stream": false,
            "voice_setting": {
                "voice_id": VOICE_SOUND_ID || "ttv-voice-2024112722551824-IG1c5u0Q",
                "speed": 1.1,
                "vol": 1,
                "pitch": 0
            }
        };

        // 发送请求
        console.log("发送TTS请求...");
        let res = http.request(url, {
            method: "POST",
            headers: headers,
            body: JSON.stringify(requestBody)
        });

        if (res.statusCode === 200) {
            let result = res.body.json();
            if (result.base_resp.status_code === 0) {
                let audioData = result.data.audio;
                let fileName = "voice_" + Date.now() + ".mp3";
                let filePath = INTERACT_FOLDER + fileName;
                
                // 修改解码方式，确保byte值在正确范围内
                let bytes = [];
                for (let i = 0; i < audioData.length; i += 2) {
                    let value = parseInt(audioData.substr(i, 2), 16);
                    // 将无符号字节(0-255)转换为有符号字节(-128到127)
                    bytes.push(value > 127 ? value - 256 : value);
                }
                
                let byteArray = java.lang.reflect.Array.newInstance(java.lang.Byte.TYPE, bytes.length);
                for (let i = 0; i < bytes.length; i++) {
                    byteArray[i] = bytes[i];
                }
                
                files.writeBytes(filePath, byteArray);
                lastVoiceReplyTime = Date.now();
            } else {
                console.error("TTS API响应错误：" + JSON.stringify(result.base_resp));
            }
        } else {
            console.error("TTS API请求失败：" + res.statusCode + " - " + res.body.string());
        }

    } catch (e) {
        console.error("生成语音回复出错：", e);
    }
}

// 语音播放处理
function handleVoice() {
    function playNext() {
        try {
            let interactFiles = java.io.File(INTERACT_FOLDER).list();
            if (!interactFiles) {
                interactFiles = [];
            }
            interactFiles = interactFiles.filter(function(name) {
                return name.endsWith('.mp3') || name.endsWith('.aac');
            });
            
            if(interactFiles.length > 0) {
                let path = INTERACT_FOLDER + interactFiles[0];
                playAudio(path, function() {
                    java.io.File(path).delete();
                    sleep(1000);
                    playNext();
                });
                return;
            }

            let introduceFiles = java.io.File(INTRODUCE_FOLDER).list();
            if (!introduceFiles) {
                introduceFiles = [];
            }
            introduceFiles = introduceFiles.filter(function(name) {
                return name.endsWith('.mp3') || name.endsWith('.aac');
            });
            
            if(introduceFiles.length > 0) {
                let randomIndex = Math.floor(Math.random() * introduceFiles.length);
                let path = INTRODUCE_FOLDER + introduceFiles[randomIndex];
                playAudio(path, function() {
                    sleep(1000);
                    playNext();
                });
            } else {
                sleep(5000);
                playNext();
            }
        } catch(e) {
            console.error("播放循环出错：", e);
            sleep(3000);
            playNext();
        }
    }

    // 启动播放循环
    threads.start(function() {
        playNext();
    });
}

// 创建必要的文件夹
function initFolders() {
    try {
        // 确保私有目录存在
        let interactDir = new java.io.File(INTERACT_FOLDER);
        if (!interactDir.exists()) {
            interactDir.mkdirs();
            console.log("创建互动语音临时文件夹：" + INTERACT_FOLDER);
        }
        
        // 外部存储的目录
        let introduceDir = new java.io.File(INTRODUCE_FOLDER);
        if (!introduceDir.exists()) {
            introduceDir.mkdirs();
            console.log("创建商品话术文件夹：" + INTRODUCE_FOLDER);
        }
    } catch(e) {
        console.error("创建文件夹失败：", e);
    }
}

// 提取用户名函数
function extractUsername(content) {
    try {
        let username = "";
        // 处理不同格式的评论
        if (content.includes("：")) {
            username = content.split("：")[0];
        } else if (content.includes(":")) {  // 处理英文冒号
            username = content.split(":")[0];
        } else if (content.includes("为主播点赞了")) {
            username = content.split("为主播点赞了")[0];
        } else {
            // 使用正则匹配"通过xxx来了"的格式
            let match = content.match(/(.*?)通过.*?来了/);
            if (match) {
                username = match[1];
            } else if (content.includes("来了")) {
                username = content.split("来了")[0];
            }
        }
        
        // 清理用户名
        username = username.trim();
        console.log("提取到用户名：" + username);
        return username;
    } catch(e) {
        console.error("提取用户名出错：", e);
        return "";
    }
}

// 初始化函数中添加prompt加载
function initSystem() {
    console.log("初始化系统...");
    
    // 初始化文件夹
    initFolders();
    
    // 加载prompt
    try {
      	let promptText = `###任务：
你是抖音直播间的控场助理，请根据现在正在直播的播报内容，回复用户的评论，做到简洁明了，不要错误回答，回了不了的问题回复 请稍等。

###播报内容：
{{introduce}}

###用户评论：
{{content}}

###注意：
1. 回复内容一定要简洁，亲切，要能够吸引用户购买。
2. 输出内容为直接回复客户的内容，不能重复用户问题，不需要包含自己的角色。

直接输出：`;
        let introduceText = files.read(PRODUCT_INTRODUCE);
        if (!introduceText) {
            console.error("未找到商品直播稿文件：" + PRODUCT_INTRODUCE);
            exit();
        }
        if (PROMPT_RAG) {
            introduceText = introduceText + "补充知识：\n" + PROMPT_RAG;
        }
        INTERACT_PROMPT = promptText.replace(/{{introduce}}/g, introduceText);
    } catch(e) {
        console.error("加载商品介绍失败：", e);
        exit();
    }
}

// 添加判断交互类型的函数
function getInteractionType(content) {
    if (content.includes("：") || content.includes(":")) {
        return INTERACTION_TYPE.COMMENT;
    } else if (content.includes("为主播点赞了")) {
        return INTERACTION_TYPE.LIKE;
    } else if (content.match(/(.*?)通过.*?来了/) || content.includes("来了")) {
        return INTERACTION_TYPE.ENTER;
    }
    return null;
}

// 主函数
function main() {
    console.log("初始化...");
    initSystem();
    
    if (TEXT_INTERACT_KEY || VOICE_INTERACT_KEY) {
        threads.start(function() {
            handleComments();
        });
    }
    
    handleVoice();
}

// 启动主程序
main();

// 使用events模块保持脚本运行
events.observeKey();

events.on("key", function(keyCode, event) {
    console.log("按键事件：" + keyCode);
});

events.on("exit", function() {
    console.log("脚本退出");
});

// 保持脚本运行
while(true) {
    sleep(1000);
}

