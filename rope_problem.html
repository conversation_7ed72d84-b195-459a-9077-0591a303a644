<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <title>剪绳子的数学规律</title>
    <style>
        body {
            font-family: "微软雅黑", Arial, sans-serif;
            background: #f6faff;
            color: #222;
            margin: 0;
            padding: 0 20px;
        }
        h1 {
            color: #3498db;
            margin-top: 30px;
        }
        .example, .summary, .table-section {
            background: #fff;
            border-radius: 10px;
            box-shadow: 0 2px 8px #e0e7ef;
            padding: 20px;
            margin: 20px 0;
        }
        .table-section table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 10px;
        }
        .table-section th, .table-section td {
            border: 1px solid #bcdffb;
            padding: 8px 12px;
            text-align: center;
        }
        .table-section th {
            background: #eaf6ff;
        }
        .highlight {
            color: #e67e22;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>剪绳子的数学规律</h1>
    <div class="example">
        <h2>例题思考</h2>
        <p>
            将一根绳子从中间剪断，会变成几根？<br>
            <span class="highlight">答：2根。</span>
        </p>
        <p>
            如果把绳子对折2次，再从中间剪断，会变成几根？<br>
            <span class="highlight">答：3根。</span>
        </p>
        <p>
            如果把绳子对折3次，再从中间剪断，会变成几根？<br>
            <span class="highlight">答：4根。</span>
        </p>
        <p>
            你发现了什么规律吗？每多折一次，剪断后就多一根！
        </p>
    </div>
    <div class="table-section">
        <h2>规律表格</h2>
        <table>
            <tr>
                <th>绳子几折</th>
                <th>剪刀数</th>
                <th>剪成几根</th>
            </tr>
            <tr>
                <td>1</td>
                <td>1</td>
                <td>2</td>
            </tr>
            <tr>
                <td>2</td>
                <td>1</td>
                <td>3</td>
            </tr>
            <tr>
                <td>3</td>
                <td>1</td>
                <td>4</td>
            </tr>
            <tr>
                <td>n</td>
                <td>1</td>
                <td>n+1</td>
            </tr>
        </table>
    </div>
    <div class="summary">
        <h2>规律总结</h2>
        <p>
            一根绳子对折 <span class="highlight">n</span> 次，从中间剪断后，会变成 <span class="highlight">n+1</span> 根。
        </p>
        <p>
            <strong>思考：</strong>为什么？<br>
            每多折一次，绳子就多出一个断点，所以总根数比折的次数多1。
        </p>
    </div>
</body>
</html> 