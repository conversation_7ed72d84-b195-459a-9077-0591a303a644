// 基础设置
console.show();
toastLog("开始运行快手养号脚本");

// 从环境变量获取总运行时长(分钟)
const { totalDuration } = hamibot.env;

// 转换总运行时长为毫秒
const TOTAL_DURATION = totalDuration * 60 * 1000;

// 视频浏览时长范围(毫秒)
const MIN_VIDEO_DURATION = 10 * 1000;  
const MAX_VIDEO_DURATION = 20 * 1000;

// 记录脚本开始时间
const START_TIME = Date.now();

// 主函数
function main() {
    try {
        console.log("=== 快手养号脚本启动 ===");
        console.log(`计划运行时长: ${totalDuration}分钟`);
        
        let videoCount = 0;
        
        while (true) {
            videoCount++;
            
            // 检查是否达到总运行时长
            if (Date.now() - START_TIME >= TOTAL_DURATION) {
                console.log("达到预设运行时长,脚本结束");
                console.log("=== 脚本结束 ===");
                console.log("时间：" + new Date().toLocaleString());
                break;
            }

            // 每次循环重新生成随机数
            let randomDuration = Math.random();
            let watchDuration = MIN_VIDEO_DURATION + randomDuration * (MAX_VIDEO_DURATION - MIN_VIDEO_DURATION);
            
            // 打印随机数和最终时长，用于调试
            console.log(`观看时长: ${(watchDuration/1000).toFixed(2)}秒`);
            
            // 等待设定的观看时长
            sleep(watchDuration);
            
            // 向上滑动切换下一个视频
            swipe(device.width / 2, device.height * 0.8, device.width / 2, device.height * 0.2, 500);
            
            // 短暂延迟等待新视频加载
            sleep(1000);
        }
        
    } catch(e) {
        console.error("脚本运行出错: " + e);
        console.error("错误堆栈: " + e.stack);
    }
}

// 启动主函数
main();