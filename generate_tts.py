# 用于根据intent_replies文件生成preset_tts文件夹
import json
import requests
import os
from time import sleep
from datetime import datetime

def call_tts_api(text):
    """调用TTS API获取音频数据"""
    url = "https://api.minimax.chat/v1/t2a_v2?GroupId=1836702808724738844"
    
    headers = {
        'Authorization': 'Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJHcm91cE5hbWUiOiLpm7fpm6giLCJVc2VyTmFtZSI6InNtYXJ0c291bGxpdmUwMDEiLCJBY2NvdW50Ijoic21hcnRzb3VsbGl2ZTAwMUAxODM2NzAyODA4NzI0NzM4ODQ0IiwiU3ViamVjdElEIjoiMTg2MjMyNTE0MjY1MTE1MDMzOSIsIlBob25lIjoiIiwiR3JvdXBJRCI6IjE4MzY3MDI4MDg3MjQ3Mzg4NDQiLCJQYWdlTmFtZSI6IiIsIk1haWwiOiIiLCJDcmVhdGVUaW1lIjoiMjAyNC0xMi0wMSAxMDoxOToyMyIsIlRva2VuVHlwZSI6MSwiaXNzIjoibWluaW1heCJ9.nOtjoe0ko2Tk3Tdyg5y9dIG6EwS20Z0EWrd2DdjrgqgxZvL_whpddTuGFCCJDFRhCA9sdxO215INkbZYyuJUxVCXNjXXvIunGPoAulzyxhIF8GD8klCrYDMQ-mvUXON-3QdH83kbMLQsqS6dldgfnojmD39fiZfID28ub25VDcn5pbTcVjEw58jzg0zY7eYn2STzI9F7CNdGCpXy6h4_Tgbim9G4tz7T66Mo_neUbvXOACE_TPuvYcOe0N58-loCK51vQv-ufDICwGyANPEOEIh474HR1O-I0JPOqGvQfgW3UuHbF_qf4A3ExnVIyoB8sTK2ByJaK0DmYoG0mf-4ow',
        'Content-Type': 'application/json'
    }
    
    data = {
        "model": "speech-01-turbo",
        "text": text,
        "stream": False,
        "voice_setting": {
            "voice_id": "meteorflymale026",
            "speed": 1,
            "vol": 1,
            "pitch": 0
        }
    }
    
    try:
        response = requests.post(url, headers=headers, json=data)
        response.raise_for_status()  # 检查请求是否成功
        result = response.json()
        
        if result.get('base_resp', {}).get('status_code') == 0:
            return result.get('data', {}).get('audio')
        else:
            print(f"API返回错误: {result}")
            return None
            
    except Exception as e:
        print(f"调用API出错: {str(e)}")
        return None

def process_intent_replies(json_file_path, output_folder, create_mp3=False):
    """处理intent_replies.json文件，生成缺失的音频文件"""
    
    # 确保输出文件夹存在
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        
    # 读取JSON文件
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except Exception as e:
        print(f"读取JSON文件失败: {str(e)}")
        return
        
    # 遍历所有意图和回复
    for intent_code, intent_data in data.get('intents', {}).items():
        print(f"\n处理意图: {intent_code}")
        
        for reply in intent_data.get('replies', []):
            audio_filename = reply.get('audio')
            if not audio_filename:
                print(f"警告: 回复缺少audio字段: {reply}")
                continue
                
            # 检查音频文件是否已存在
            audio_path = os.path.join(output_folder, audio_filename)
            if os.path.exists(audio_path):
                print(f"跳过已存在的音频文件: {audio_filename}")
                continue
                
            # 调用API生成新的音频文件
            print(f"正在生成音频: {audio_filename}")
            print(f"文本内容: {reply.get('text', '')}")
            
            hex_data = call_tts_api(reply['text'])
            if hex_data:
                try:
                    with open(audio_path, 'w') as f:
                        f.write(hex_data)
                    print(f"成功生成音频文件: {audio_filename}")
                    if create_mp3:
                        save_audio_file(hex_data, output_folder, audio_filename)
                    # 添加延时避免API调用过于频繁
                    sleep(1)
                except Exception as e:
                    print(f"保存音频文件失败: {str(e)}")
            else:
                print(f"生成音频失败: {audio_filename}")

def convert_hex_to_bytes(hex_string):
    # 移除可能的空格和换行符
    hex_string = hex_string.strip()
    
    try:
        # 每两个字符转换为一个字节
        byte_data = bytes.fromhex(hex_string)
        
        # 验证所有值是否在有效范围内
        for b in byte_data:
            if not 0 <= b <= 255:
                raise ValueError(f"Invalid byte value: {b}")
                
        return byte_data
        
    except ValueError as e:
        print(f"转换错误: {e}")
        return None

def save_audio_file(audio_data: str, output_dir: str = ".", file_name: str = None) -> str:
    """
    将音频数据保存为MP3文件
    
    参数:
        audio_data: 十六进制格式的音频数据字符串
        output_dir: 输出目录，默认为当前目录
    
    返回:
        生成的文件路径
    """
    # 生成文件名
    if file_name is None:
        file_name = f"voice_{int(datetime.now().timestamp() * 1000)}.mp3"
    else:
        file_name = file_name.replace(".hex", ".mp3")   
    file_path = os.path.join(output_dir, file_name)
    
    # 转换数据并写入文件
    try:
        byte_data = convert_hex_to_bytes(audio_data)
        if byte_data:
            with open(file_path, 'wb') as f:
                f.write(byte_data)
            print(f"音频文件已保存: {file_path}")
            return file_path
        else:
            print("无效的音频数据")
            return ""
    except Exception as e:
        print(f"保存音频文件时出错: {str(e)}")
        return ""

def main():
    # 设置文件路径
    json_file = "D:\work\ds\素材\带货\血橙\intent_replies.json"  # JSON文件路径
    output_folder = "D:\work\ds\素材\带货\血橙\preset_tts"       # 输出文件夹路径
    
    # 处理文件
    process_intent_replies(json_file, output_folder, create_mp3=False)

if __name__ == "__main__":
    main()