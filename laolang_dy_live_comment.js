// 基础设置
console.show();
toastLog("开始运行");

// 定义文件夹路径
var INTERACT_FOLDER = "/storage/emulated/0/Download/douyinLive/resource/productTest/voice/interact/";
var INTRODUCE_FOLDER = "/storage/emulated/0/Download/douyinLive/resource/productTest/voice/introduce/";

// 音频播放函数
function playAudio(path, callback) {
    var player = new android.media.MediaPlayer();
    try {
        player.setDataSource(path);
        player.prepare();
        player.start();
        
        // 获取音频时长
        var duration = player.getDuration();
        
        // 使用定时器检查播放状态
        var checkInterval = setInterval(function() {
            try {
                if (!player.isPlaying()) {
                    clearInterval(checkInterval);
                    try {
                        player.reset();
                        player.release();
                        if (callback) {
                            callback();
                        }
                    } catch(e) {
                        console.error("释放播放器出错：", e);
                        if (callback) {
                            callback(e);
                        }
                    }
                }
            } catch(e) {
                clearInterval(checkInterval);
                console.error("播放状态检查出错：", e);
                try {
                    player.reset();
                    player.release();
                } catch(e2) {
                    console.error("释放播放器出错：", e2);
                }
                if (callback) {
                    callback(e);
                }
            }
        }, 100);
        
    } catch(e) {
        console.error("播放初始化出错：", e);
        try {
            player.reset();
            player.release();
        } catch(e2) {
            console.error("释放播放器出错：", e2);
        }
        if (callback) {
            callback(e);
        }
    }
}

// 评论互动处理
function handleComments() {
    console.log("开始处理评论");
    var history = {};
    var lastProcessedTime = Date.now();
    
    while(true) {
        try {
            console.log("开始新一轮评论检查...");
            
            // 查找评论列表
            let commentList = className("androidx.recyclerview.widget.RecyclerView").findOne(2000);
            if (!commentList) {
                console.log("未找到评论列表，继续等待...");
                sleep(1000);
                continue;
            }

            // 获取所有评论项
            let comments = commentList.children();
            if (comments.length === 0) {
                console.log("暂无评论");
                sleep(1000);
                continue;
            }

            console.log("找到评论数量：" + comments.length);

            // 遍历评论
            for (let i = 0; i < comments.length; i++) {
                let commentItem = comments[i];
                if (!commentItem) continue;

                // 获取评论文本
                let textView = commentItem.findOne(className("android.widget.TextView"));
                if (!textView) continue;

                let content = textView.text();
                if (!content) {
                    content = commentItem.desc();
                }
                if (!content) continue;

                // 处理评论内容
                content = content.replace(/\u200E/g, "").trim();
                
                // 只处理包含"："的消息
                if (!content.includes("：")) {
                    continue;
                }
                
                // 跳过自己发送的消息
                if (content.includes("我的：")) {
                    continue;
                }
                
                // 检查是否是历史评论
                if (history[content]) {
                    continue;
                }

                // 获取评论时间
                let timeView = commentItem.findOne(className("android.widget.TextView").textMatches(/.*(分钟前|刚刚).*/));
                if (timeView) {
                    let timeText = timeView.text();
                    console.log("评论时间：" + timeText);
                    // 只处理"刚刚"或"1分钟前"的评论
                    if (!timeText.includes("刚刚") && !timeText.includes("1分钟前")) {
                        console.log("跳过旧评论：" + content + " (" + timeText + ")");
                        history[content] = true;
                        continue;
                    }
                } else {
                    console.log("未找到时间信息，默认为新评论");
                }

                console.log("发现新评论：" + content);

                // 提取用户名
                let username = extractUsername(content);
                if (username) {
                    console.log("评论用户：" + username);
                    
                    // 生成回复内容
                    let reply = generateReply(content);
                    if (reply) {
                        console.log("准备回复：" + reply);
                        // 发送回复
                        sendReply(commentItem, reply);
                        
                        // 记录已处理的评论
                        history[content] = true;
                    }
                }
            }

            sleep(5000);

        } catch(e) {
            console.error("评论处理出错：", e);
            console.error("错误详情：", e.stack);
            sleep(5000);
        }
    }
}

// 生成回复内容
function generateReply(content) {
    // 这里可以根据评论内容生成相应的回复
    // 示例：简单的关键词匹配
    if (content.includes("价格")) {
        return "亲，详情请看直播间介绍哦~";
    } else if (content.includes("发货")) {
        return "当天下单当天发货，全国包邮哦~";
    } else if (content.includes("质量")) {
        return "我们的产品都是正品保证，请放心购买~";
    } else if (content.includes("优惠") || content.includes("便宜")) {
        return "现在正在做活动，下单立减，先到先得哦~";
    } else {
        return "感谢您的支持，欢迎关注直播间~";
    }
    
    // 如果没有匹配到关键词，返回null表示不需要回复
    return null;
}

// 在发送回复前分析结构
function sendReply(commentItem, content) {
    try {
        console.log("准备发送回复：" + content);
        
        // 查找评论文本元素
        let textView = commentItem.findOne(className("android.widget.TextView"));
        if (!textView) {
            console.log("未找到评论文本元素");
            return;
        }
        
        // 直接点击文本元素
        textView.click();
        console.log("点击评论文本完成");
        sleep(1000);

        // 查找可点击的"回复TA"按钮
        let menuItems = className("android.widget.LinearLayout").find();
        for (let item of menuItems) {
            if (item.clickable() && item.findOne(text("回复TA"))) {
                console.log("找到回复TA按钮");
                item.click();
                sleep(1000);
                break;
            }
        }

        // 查找输入框
        let inputBox = className("android.widget.EditText").findOne(2000);
        if (!inputBox) {
            console.log("未找到输入框");
            return;
        }

        // 设置回复内容
        // let originalText = inputBox.text();
        // inputBox.setText(originalText + " " + content);
        // 在原有内容后追加回复内容
        inputBox.click();  // 先点击输入框
        sleep(500);
        setClip(content);  // 设置剪贴板内容
        sleep(500);
        paste();  // 粘贴内容
        console.log("粘贴回复内容完成");
        sleep(1000);

        // 点击发送
        let sendBtn = text("发送").findOne(2000);
        if (!sendBtn) {
            console.log("未找到发送按钮");
            return;
        }
        sendBtn.click();
        console.log("回复发送成功：" + content);
        sleep(1000);

    } catch(e) {
        console.error("发送回复失败：", e);
        console.error("错误详情：", e.stack);
    }
}

// 语音播放处理
function handleVoice() {
    function playNext() {
        try {
            var interactFiles = java.io.File(INTERACT_FOLDER).list();
            if (!interactFiles) {
                interactFiles = [];
            }
            interactFiles = interactFiles.filter(function(name) {
                return name.endsWith('.mp3') || name.endsWith('.aac');
            });
            
            if(interactFiles.length > 0) {
                var path = INTERACT_FOLDER + interactFiles[0];
                playAudio(path, function() {
                    java.io.File(path).delete();
                    sleep(1000);
                    playNext();
                });
                return;
            }

            var introduceFiles = java.io.File(INTRODUCE_FOLDER).list();
            if (!introduceFiles) {
                introduceFiles = [];
            }
            introduceFiles = introduceFiles.filter(function(name) {
                return name.endsWith('.mp3') || name.endsWith('.aac');
            });
            
            if(introduceFiles.length > 0) {
                var randomIndex = Math.floor(Math.random() * introduceFiles.length);
                var path = INTRODUCE_FOLDER + introduceFiles[randomIndex];
                playAudio(path, function() {
                    sleep(1000);
                    playNext();
                });
            } else {
                sleep(5000);
                playNext();
            }
        } catch(e) {
            console.error("播放循环出错：", e);
            sleep(3000);
            playNext();
        }
    }

    // 启动播放循环
    threads.start(function() {
        playNext();
    });
}

// 创建必要的文件夹
function initFolders() {
    try {
        var interactDir = new java.io.File(INTERACT_FOLDER);
        var introduceDir = new java.io.File(INTRODUCE_FOLDER);
        
        if (!interactDir.exists()) {
            interactDir.mkdirs();
            console.log("创建互动话术文件夹：" + INTERACT_FOLDER);
        }
        if (!introduceDir.exists()) {
            introduceDir.mkdirs();
            console.log("创建商品话术文件夹：" + INTRODUCE_FOLDER);
        }
    } catch(e) {
        console.error("创建文件夹失败：", e);
    }
}

// 提取用户名函数
function extractUsername(content) {
    try {
        let username = "";
        // 处理不同格式的评论
        if (content.includes("：")) {
            username = content.split("：")[0];
        } else if (content.includes(":")) {  // 处理英文冒号
            username = content.split(":")[0];
        } else if (content.includes("为主播点赞了")) {  // 处理英文冒号
            username = content.split("为主播点赞了")[0];
        } else {
            // 使用正则匹配"通过xxx来了"的格式
            let match = content.match(/(.*?)通过.*?来了/);
            if (match) {
                username = match[1];
            } else if (content.includes("来了")) {
                username = content.split("来了")[0];
            }
        }
        
        // 清理用户名
        username = username.trim();
        console.log("提取到用户名：" + username);
        return username;
    } catch(e) {
        console.error("提取用户名出错：", e);
        return "";
    }
}

// 主函数
function main() {
    console.log("初始化...");
    initFolders();
    
    threads.start(function() {
        handleComments();
    });
    
    handleVoice();  // 直接调用，不再开新线程
}

// 启动主程序
main();

// 使用events模块保持脚本运行
events.observeKey();

events.on("key", function(keyCode, event) {
    console.log("按键事件：" + keyCode);
});

events.on("exit", function() {
    console.log("脚本退出");
});

// 保持脚本运行
while(true) {
    sleep(1000);
}
