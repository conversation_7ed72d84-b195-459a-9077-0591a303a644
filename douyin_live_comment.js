// 请确保已获得无障碍权限
auto.waitFor();

// 主程序
function main() {
    // 确保在抖音直播间界面
    if (!textContains("直播间").exists()) {
        toast("请先进入抖音直播间");
        exit();
    }

    // 监听评论区
    while(true) {
        // 获取评论列表容器
        let commentList = id("com.ss.android.ugc.aweme:id/live_comment_list").findOne();
        if (commentList) {
            // 获取最新的评论
            let comments = commentList.children();
            if (comments.length > 0) {
                // 获取最新的一条评论
                let lastComment = comments[comments.length - 1];
                
                // 提取用户名和评论内容
                let username = lastComment.findOne(id("com.ss.android.ugc.aweme:id/user_name")).text();
                let content = lastComment.findOne(id("com.ss.android.ugc.aweme:id/content")).text();
                
                // 回复评论
                replyToComment(username, content);
            }
        }
        
        // 等待一秒再次检查
        sleep(1000);
    }
}

// 回复评论函数
function replyToComment(username, content) {
    // 点击评论输入框
    let commentInput = id("com.ss.android.ugc.aweme:id/comment_input").findOne();
    commentInput.click();
    sleep(500);
    
    // 输入回复内容
    let replyText = "@" + username + " 感谢您的评论！";
    setText(replyText);
    sleep(500);
    
    // 点击发送按钮
    let sendBtn = id("com.ss.android.ugc.aweme:id/send_btn").findOne();
    sendBtn.click();
    sleep(1000);
}

// 启动主程序
main(); 
auto.waitFor();

// 主程序
function main() {
    // 确保在抖音直播间界面
    if (!textContains("直播间").exists()) {
        toast("请先进入抖音直播间");
        exit();
    }

    // 监听评论区
    while(true) {
        // 获取评论列表容器
        let commentList = id("com.ss.android.ugc.aweme:id/live_comment_list").findOne();
        if (commentList) {
            // 获取最新的评论
            let comments = commentList.children();
            if (comments.length > 0) {
                // 获取最新的一条评论
                let lastComment = comments[comments.length - 1];
                
                // 提取用户名和评论内容
                let username = lastComment.findOne(id("com.ss.android.ugc.aweme:id/user_name")).text();
                let content = lastComment.findOne(id("com.ss.android.ugc.aweme:id/content")).text();
                
                // 回复评论
                replyToComment(username, content);
            }
        }
        
        // 等待一秒再次检查
        sleep(1000);
    }
}

// 回复评论函数
function replyToComment(username, content) {
    // 点击评论输入框
    let commentInput = id("com.ss.android.ugc.aweme:id/comment_input").findOne();
    commentInput.click();
    sleep(500);
    
    // 输入回复内容
    let replyText = "@" + username + " 感谢您的评论！";
    setText(replyText);
    sleep(500);
    
    // 点击发送按钮
    let sendBtn = id("com.ss.android.ugc.aweme:id/send_btn").findOne();
    sendBtn.click();
    sleep(1000);
}

// 启动主程序
main();